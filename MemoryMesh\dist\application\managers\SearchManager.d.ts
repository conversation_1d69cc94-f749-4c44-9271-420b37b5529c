import { ISearchManager } from './interfaces/ISearchManager.js';
import { IManager } from './interfaces/IManager.js';
import type { Graph } from '../../core/index.js';
/**
 * Implements search-related operations for the knowledge graph.
 * Provides functionality for searching nodes and retrieving graph data.
 */
export declare class SearchManager extends IManager implements ISearchManager {
    /**
     * Searches for nodes in the knowledge graph based on a query.
     * Includes both matching nodes and their immediate neighbors.
     */
    searchNodes(query: string): Promise<Graph>;
    /**
     * Retrieves specific nodes and their immediate neighbors from the knowledge graph.
     */
    openNodes(names: string[]): Promise<Graph>;
    /**
     * Reads and returns the entire knowledge graph.
     */
    readGraph(): Promise<Graph>;
    /**
     * Initializes the search manager.
     */
    initialize(): Promise<void>;
}
