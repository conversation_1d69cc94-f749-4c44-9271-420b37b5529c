{"version": 3, "file": "GraphValidator.js", "sourceRoot": "", "sources": ["../../../src/core/graph/GraphValidator.ts"], "names": [], "mappings": "AAAA,mCAAmC;AAKnC,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,cAAc;IACvB;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAAY,EAAE,QAAgB;QACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,KAAY,EAAE,QAAgB;QAC1D,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,oCAAoC,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,IAAU;QAClD,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC5B,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAC3B,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;YACvB,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CACtC,EAAE,CAAC;YACA,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAU;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,IAAmB;QAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,SAAkB;QAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAU;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC5B,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,KAAa;QACrD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,qBAAqB;QACrB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/D,qBAAqB;QACrB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,+BAA+B;AAC/B,MAAM,CAAC,MAAM,EACT,kBAAkB,EAClB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,sBAAsB,EACtB,sBAAsB,EACzB,GAAG,cAAc,CAAC"}