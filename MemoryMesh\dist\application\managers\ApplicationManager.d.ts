import type { IStorage } from '../../infrastructure/index.js';
import type { Node, Edge, Graph, MetadataAddition, MetadataDeletion, MetadataResult } from '../../core/index.js';
import type { EdgeFilter, GetEdgesResult, OpenNodesResult } from '../../shared/index.js';
/**
 * Main facade that coordinates between specialized managers
 */
export declare class ApplicationManager {
    private readonly graphManager;
    private readonly searchManager;
    private readonly transactionManager;
    constructor(storage?: IStorage);
    addNodes(nodes: Node[]): Promise<Node[]>;
    updateNodes(nodes: Partial<Node>[]): Promise<Node[]>;
    deleteNodes(nodeNames: string[]): Promise<void>;
    addEdges(edges: Edge[]): Promise<Edge[]>;
    updateEdges(edges: Edge[]): Promise<Edge[]>;
    deleteEdges(edges: Edge[]): Promise<void>;
    getEdges(filter?: EdgeFilter): Promise<GetEdgesResult>;
    addMetadata(metadata: MetadataAddition[]): Promise<MetadataResult[]>;
    deleteMetadata(deletions: MetadataDeletion[]): Promise<void>;
    readGraph(): Promise<Graph>;
    searchNodes(query: string): Promise<OpenNodesResult>;
    openNodes(names: string[]): Promise<OpenNodesResult>;
    beginTransaction(): Promise<void>;
    commit(): Promise<void>;
    rollback(): Promise<void>;
    withTransaction<T>(operation: () => Promise<T>): Promise<T>;
    addRollbackAction(action: () => Promise<void>, description: string): Promise<void>;
    isInTransaction(): boolean;
    getCurrentGraph(): Graph;
}
