import { EventEmitter, IStorage } from '../../../infrastructure/index.js';
/**
 * Abstract base class for all manager interfaces.
 * Provides event emission capabilities and implements common initialization.
 */
export declare abstract class IManager extends EventEmitter {
    /**
     * The storage instance used by the manager.
     */
    protected storage: IStorage;
    /**
     * Creates an instance of IManager.
     * @param storage - The storage mechanism to use for persisting the knowledge graph.
     * @throws {Error} If attempting to instantiate the abstract class directly.
     */
    constructor(storage: IStorage);
    /**
     * Initializes the manager by emitting the 'initialized' event.
     * Common implementation for all manager classes.
     */
    initialize(): Promise<void>;
}
