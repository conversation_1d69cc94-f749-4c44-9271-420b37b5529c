import type { Graph } from '../../core/index.js';
/**
 * 簡化的節點結構，僅包含基本資訊
 */
export interface SimplifiedNode {
    name: string;
    nodeType: string;
}
/**
 * 簡化的邊緣結構，保留連接資訊
 */
export interface SimplifiedEdge {
    from: string;
    to: string;
    edgeType: string;
}
/**
 * 簡化的圖譜結構
 */
export interface SimplifiedGraph {
    nodes: SimplifiedNode[];
    edges: SimplifiedEdge[];
    summary: {
        totalNodes: number;
        totalEdges: number;
        nodeTypes: string[];
        edgeTypes: string[];
    };
}
/**
 * 將完整的圖譜轉換為簡化格式
 * 移除 metadata 和其他冗餘資訊，僅保留核心連接結構
 */
export declare function simplifyGraph(graph: Graph): SimplifiedGraph;
/**
 * 格式化簡化圖譜為易讀的文本格式
 * 進一步減少 JSON 結構層級和冗餘符號
 */
export declare function formatSimplifiedGraphAsText(simplifiedGraph: SimplifiedGraph): string;
/**
 * 計算數據精簡比例
 */
export declare function calculateCompressionRatio(originalGraph: Graph, simplifiedGraph: SimplifiedGraph): {
    originalSize: number;
    simplifiedSize: number;
    compressionRatio: number;
    savedBytes: number;
};
