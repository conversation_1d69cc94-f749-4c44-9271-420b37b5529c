import { EventEmitter } from '../../infrastructure/index.js';
import type { INodeManager, IEdgeManager, IMetadataManager } from '../index.js';
import type { Node, Edge, MetadataAddition, MetadataDeletion, MetadataResult } from '../../core/index.js';
import type { <PERSON>Filter, GetEdgesResult } from '../../shared/index.js';
export declare class GraphOperations extends EventEmitter {
    private nodeManager;
    private edgeManager;
    private metadataManager;
    constructor(nodeManager: INodeManager, edgeManager: IEdgeManager, metadataManager: IMetadataManager);
    addNodes(nodes: Node[]): Promise<Node[]>;
    updateNodes(nodes: Partial<Node>[]): Promise<Node[]>;
    deleteNodes(nodeNames: string[]): Promise<void>;
    addEdges(edges: Edge[]): Promise<Edge[]>;
    updateEdges(edges: Edge[]): Promise<Edge[]>;
    deleteEdges(edges: Edge[]): Promise<void>;
    getEdges(filter?: EdgeFilter): Promise<GetEdgesResult>;
    addMetadata(metadata: MetadataAddition[]): Promise<MetadataResult[]>;
    deleteMetadata(deletions: MetadataDeletion[]): Promise<void>;
}
