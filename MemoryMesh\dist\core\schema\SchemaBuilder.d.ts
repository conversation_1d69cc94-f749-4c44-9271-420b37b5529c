/**
 * Schema property configuration
 */
export interface SchemaPropertyConfig {
    type: string;
    description: string;
    enum?: string[];
    items?: Partial<SchemaPropertyConfig>;
    properties?: Record<string, SchemaPropertyConfig>;
    required?: string[];
}
/**
 * Relationship configuration
 */
export interface RelationshipConfig {
    edgeType: string;
    nodeType?: string;
    description?: string;
}
/**
 * Metadata configuration
 */
export interface MetadataConfig {
    requiredFields: string[];
    optionalFields: string[];
    excludeFields: string[];
}
/**
 * Schema configuration
 */
export interface SchemaConfig {
    name: string;
    description: string;
    inputSchema: {
        type: string;
        properties: Record<string, {
            type: string;
            properties: Record<string, SchemaPropertyConfig>;
            required: string[];
            additionalProperties: boolean | SchemaPropertyConfig;
        }>;
        required: string[];
    };
    relationships: Record<string, RelationshipConfig>;
    metadataConfig: MetadataConfig;
}
/**
 * Facilitates the construction and manipulation of schemas for nodes in the knowledge graph.
 */
export declare class SchemaBuilder {
    private schema;
    private relationships;
    private metadataConfig;
    /**
     * Creates an instance of SchemaBuilder.
     */
    constructor(name: string, description: string);
    /**
     * Adds a string property to the schema with an optional enum.
     */
    addStringProperty(name: string, description: string, required?: boolean, enumValues?: string[] | null): SchemaBuilder;
    /**
     * Adds an array property to the schema with optional enum values for items.
     */
    addArrayProperty(name: string, description: string, required?: boolean, enumValues?: string[] | null): SchemaBuilder;
    /**
     * Adds a relationship definition to the schema.
     */
    addRelationship(propertyName: string, edgeType: string, description: string, nodeType?: string | null): SchemaBuilder;
    /**
     * Sets whether additional properties are allowed in the schema.
     */
    allowAdditionalProperties(allowed: boolean): SchemaBuilder;
    /**
     * Creates an update schema based on the current schema.
     */
    createUpdateSchema(excludeFields?: Set<string>): SchemaConfig;
    /**
     * Builds and returns the final schema object.
     */
    build(): SchemaConfig;
}
