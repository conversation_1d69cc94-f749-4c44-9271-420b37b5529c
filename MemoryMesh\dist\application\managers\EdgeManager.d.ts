import { IEdgeManager } from '../managers/interfaces/IEdgeManager.js';
import type { Edge } from '../../core/index.js';
import type { EdgeUpdate, EdgeFilter } from '../../shared/index.js';
/**
 * Implements edge-related operations for the knowledge graph.
 * Includes adding, updating, deleting, and retrieving edges.
 */
export declare class EdgeManager extends IEdgeManager {
    /**
     * Adds new edges to the knowledge graph.
     */
    addEdges(edges: Edge[]): Promise<Edge[]>;
    /**
     * Updates existing edges in the knowledge graph.
     */
    updateEdges(edges: EdgeUpdate[]): Promise<Edge[]>;
    /**
     * Deletes edges from the knowledge graph.
     */
    deleteEdges(edges: Edge[]): Promise<void>;
    /**
     * Retrieves edges from the knowledge graph based on filter criteria.
     */
    getEdges(filter?: EdgeFilter): Promise<Edge[]>;
}
