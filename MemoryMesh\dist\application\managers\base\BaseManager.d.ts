import type { IStorage } from '../../../infrastructure/index.js';
/**
 * Base class that handles initialization and common functionality
 */
export declare abstract class BaseManager {
    protected readonly storage: IStorage;
    constructor(storage?: IStorage);
    protected createManagers(): {
        nodeManager: import("../../index.js").INodeManager;
        edgeManager: import("../../index.js").IEdgeManager;
        metadataManager: import("../../index.js").IMetadataManager;
        searchManager: import("../../index.js").ISearchManager;
        transactionManager: import("../../index.js").ITransactionManager;
    };
}
