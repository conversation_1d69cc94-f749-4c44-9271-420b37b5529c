{"name": "add_organization", "description": "Add a new organization, company, team, or group to the knowledge graph. Suitable for businesses, departments, clubs, or any collective entity.", "properties": {"name": {"type": "string", "description": "Organization's name or title", "required": true}, "type": {"type": "string", "description": "Type of organization", "required": true, "enum": ["Company", "Department", "Team", "Project Group", "Committee", "Club", "Association", "Government", "NGO", "Other"]}, "description": {"type": "string", "description": "Detailed description of the organization", "required": true}, "status": {"type": "string", "description": "Current operational status", "required": true, "enum": ["Active", "Inactive", "Dissolved", "Pending", "Suspended"]}, "leader": {"type": "string", "description": "Leader, CEO, or head of the organization", "required": false, "relationship": {"edgeType": "led_by", "description": "Leadership relationship"}}, "parentOrganization": {"type": "string", "description": "Parent or umbrella organization", "required": false, "relationship": {"edgeType": "part_of", "description": "Organizational hierarchy"}}, "headquarters": {"type": "string", "description": "Main location or headquarters", "required": false, "relationship": {"edgeType": "headquartered_at", "description": "Primary location"}}, "foundedDate": {"type": "string", "description": "Date when the organization was founded", "required": false}, "size": {"type": "string", "description": "Size of the organization (number of members/employees)", "required": false}, "industry": {"type": "string", "description": "Industry or sector", "required": false}, "goals": {"type": "array", "description": "Primary goals or objectives", "required": false}, "services": {"type": "array", "description": "Services or products offered", "required": false}, "contactInfo": {"type": "array", "description": "Contact information", "required": false}, "website": {"type": "string", "description": "Official website URL", "required": false}, "budget": {"type": "string", "description": "Budget or financial information", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}}, "additionalProperties": true}