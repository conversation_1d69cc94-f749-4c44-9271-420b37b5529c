{"name": "add_character_main", "description": "Add main character (protagonist) with detailed personality, background, and development arc. Used by AI Worker #1 for core character creation.", "properties": {"name": {"type": "string", "description": "Character's full name", "required": true}, "role": {"type": "string", "description": "Character's role in the story", "required": true, "enum": ["Protagonist", "Love Interest", "Deuteragonist", "Co-Protagonist"]}, "age": {"type": "string", "description": "Character's age", "required": true}, "gender": {"type": "string", "description": "Character's gender identity", "required": false}, "physicalDescription": {"type": "array", "description": "Physical appearance details", "required": true}, "personality": {"type": "array", "description": "Core personality traits and characteristics", "required": true}, "background": {"type": "string", "description": "Character's background story and history", "required": true}, "occupation": {"type": "string", "description": "Character's job or primary activity", "required": false}, "goals": {"type": "array", "description": "Character's primary goals and motivations", "required": true}, "fears": {"type": "array", "description": "Character's fears and anxieties", "required": false}, "strengths": {"type": "array", "description": "Character's strengths and positive qualities", "required": true}, "weaknesses": {"type": "array", "description": "Character's flaws and weaknesses", "required": true}, "secrets": {"type": "array", "description": "Hidden aspects or secrets about the character", "required": false}, "characterArc": {"type": "string", "description": "Planned character development and growth arc", "required": true}, "speechPattern": {"type": "string", "description": "How the character speaks and communicates", "required": false}, "habits": {"type": "array", "description": "Character's habits and mannerisms", "required": false}, "relationships": {"type": "array", "description": "Key relationships with other characters", "required": false, "relationship": {"edgeType": "has_relationship_with", "description": "Character relationships"}}, "currentLocation": {"type": "string", "description": "Character's current or primary location", "required": false, "relationship": {"edgeType": "located_at", "description": "Character location"}}, "affiliations": {"type": "array", "description": "Organizations or groups the character belongs to", "required": false, "relationship": {"edgeType": "member_of", "description": "Character affiliations"}}, "status": {"type": "string", "description": "Character's current status in the story", "required": true, "enum": ["Active", "Introduced", "Developing", "Established", "Completed Arc"]}, "notes": {"type": "array", "description": "Additional character notes", "required": false}}, "additionalProperties": true}