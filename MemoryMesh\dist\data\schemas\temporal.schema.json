{"name": "add_temporal", "description": "Add a temporal state, time period, or schedule to the knowledge graph. Suitable for tracking time-based information, schedules, or historical records.", "properties": {"name": {"type": "string", "description": "Name or identifier for this temporal record", "required": true}, "type": {"type": "string", "description": "Type of temporal record", "required": true, "enum": ["Schedule", "Timeline", "Period", "Milestone", "Deadline", "Historical Record", "Current State", "Other"]}, "startDateTime": {"type": "string", "description": "Start date and time", "required": false}, "endDateTime": {"type": "string", "description": "End date and time", "required": false}, "duration": {"type": "string", "description": "Duration or time span", "required": false}, "frequency": {"type": "string", "description": "Frequency or recurrence pattern", "required": false, "enum": ["One-time", "Daily", "Weekly", "Monthly", "Quarterly", "Yearly", "Custom"]}, "status": {"type": "string", "description": "Current status", "required": false, "enum": ["Scheduled", "Active", "Completed", "Cancelled", "Overdue"]}, "description": {"type": "string", "description": "Description of the temporal record", "required": false}, "relatedEntities": {"type": "array", "description": "Entities associated with this temporal record", "required": false, "relationship": {"edgeType": "scheduled_for", "description": "Temporal associations"}}, "conditions": {"type": "array", "description": "Environmental or contextual conditions", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}}, "additionalProperties": true}