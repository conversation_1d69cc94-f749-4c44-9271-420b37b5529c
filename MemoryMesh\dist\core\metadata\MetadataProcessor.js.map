{"version": 3, "file": "MetadataProcessor.js", "sourceRoot": "", "sources": ["../../../src/core/metadata/MetadataProcessor.ts"], "names": [], "mappings": "AAAA,yCAAyC;AAIzC,MAAM,OAAO,iBAAiB;IAC1B;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAAa;QACnC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO;YACH,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE;YAC1C,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;SAChD,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAW,EAAE,KAAkC;QACtE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,GAAG,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAkB;QACtC,IAAI,CAAC;YACD,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,GAAG,cAA0B;QAC9C,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAExC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC9B,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,QAAkB,EAAE,GAAW;QAC9C,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC9C,OAAO,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,QAAkB,EAAE,GAAW;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAChD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAkB;QACvC,MAAM,GAAG,GAAG,IAAI,GAAG,EAAkB,CAAC;QAEtC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACf,CAAC;CACJ"}