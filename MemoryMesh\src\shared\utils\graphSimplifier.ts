// src/shared/utils/graphSimplifier.ts

import type {Graph, Node, Edge} from '@core/index.js';

/**
 * 簡化的節點結構，僅包含基本資訊
 */
export interface SimplifiedNode {
    name: string;
    nodeType: string;
}

/**
 * 簡化的邊緣結構，保留連接資訊
 */
export interface SimplifiedEdge {
    from: string;
    to: string;
    edgeType: string;
}

/**
 * 簡化的圖譜結構
 */
export interface SimplifiedGraph {
    nodes: SimplifiedNode[];
    edges: SimplifiedEdge[];
    summary: {
        totalNodes: number;
        totalEdges: number;
        nodeTypes: string[];
        edgeTypes: string[];
    };
}

/**
 * 將完整的圖譜轉換為簡化格式
 * 移除 metadata 和其他冗餘資訊，僅保留核心連接結構
 */
export function simplifyGraph(graph: Graph): SimplifiedGraph {
    // 簡化節點：僅保留 name 和 nodeType
    const simplifiedNodes: SimplifiedNode[] = graph.nodes.map(node => ({
        name: node.name,
        nodeType: node.nodeType
    }));

    // 簡化邊緣：僅保留連接資訊
    const simplifiedEdges: SimplifiedEdge[] = graph.edges.map(edge => ({
        from: edge.from,
        to: edge.to,
        edgeType: edge.edgeType
    }));

    // 生成統計摘要
    const nodeTypes = [...new Set(graph.nodes.map(node => node.nodeType))].sort();
    const edgeTypes = [...new Set(graph.edges.map(edge => edge.edgeType))].sort();

    return {
        nodes: simplifiedNodes,
        edges: simplifiedEdges,
        summary: {
            totalNodes: simplifiedNodes.length,
            totalEdges: simplifiedEdges.length,
            nodeTypes,
            edgeTypes
        }
    };
}

/**
 * 格式化簡化圖譜為易讀的文本格式
 * 進一步減少 JSON 結構層級和冗餘符號
 */
export function formatSimplifiedGraphAsText(simplifiedGraph: SimplifiedGraph): string {
    const lines: string[] = [];
    
    // 添加摘要資訊
    lines.push(`=== 圖譜摘要 ===`);
    lines.push(`節點總數: ${simplifiedGraph.summary.totalNodes}`);
    lines.push(`邊緣總數: ${simplifiedGraph.summary.totalEdges}`);
    lines.push(`節點類型: ${simplifiedGraph.summary.nodeTypes.join(', ')}`);
    lines.push(`關係類型: ${simplifiedGraph.summary.edgeTypes.join(', ')}`);
    lines.push('');

    // 按類型分組顯示節點
    lines.push(`=== 節點列表 ===`);
    const nodesByType = new Map<string, string[]>();
    
    simplifiedGraph.nodes.forEach(node => {
        if (!nodesByType.has(node.nodeType)) {
            nodesByType.set(node.nodeType, []);
        }
        nodesByType.get(node.nodeType)!.push(node.name);
    });

    for (const [nodeType, nodeNames] of nodesByType.entries()) {
        lines.push(`${nodeType}: ${nodeNames.join(', ')}`);
    }
    lines.push('');

    // 按類型分組顯示邊緣
    lines.push(`=== 關係連接 ===`);
    const edgesByType = new Map<string, string[]>();
    
    simplifiedGraph.edges.forEach(edge => {
        if (!edgesByType.has(edge.edgeType)) {
            edgesByType.set(edge.edgeType, []);
        }
        edgesByType.get(edge.edgeType)!.push(`${edge.from} → ${edge.to}`);
    });

    for (const [edgeType, connections] of edgesByType.entries()) {
        lines.push(`${edgeType}:`);
        connections.forEach(connection => {
            lines.push(`  ${connection}`);
        });
    }

    return lines.join('\n');
}

/**
 * 計算數據精簡比例
 */
export function calculateCompressionRatio(originalGraph: Graph, simplifiedGraph: SimplifiedGraph): {
    originalSize: number;
    simplifiedSize: number;
    compressionRatio: number;
    savedBytes: number;
} {
    const originalSize = JSON.stringify(originalGraph).length;
    const simplifiedSize = JSON.stringify(simplifiedGraph).length;
    const compressionRatio = ((originalSize - simplifiedSize) / originalSize * 100);
    const savedBytes = originalSize - simplifiedSize;

    return {
        originalSize,
        simplifiedSize,
        compressionRatio: Math.round(compressionRatio * 100) / 100,
        savedBytes
    };
}
