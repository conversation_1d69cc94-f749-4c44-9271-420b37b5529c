import type { ApplicationManager } from '../../application/index.js';
interface ToolCallRequest {
    params: {
        name: string;
        arguments: Record<string, any>;
    };
}
/**
 * Handles incoming tool call requests by routing them to the appropriate handler
 */
export declare function handleCallToolRequest(request: ToolCallRequest, knowledgeGraphManager: ApplicationManager): Promise<any>;
export {};
