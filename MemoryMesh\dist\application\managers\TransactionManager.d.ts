import { ITransactionManager } from './interfaces/ITransactionManager.js';
import { IManager } from './interfaces/IManager.js';
import type { Graph } from '../../core/index.js';
import type { IStorage } from '../../infrastructure/index.js';
/**
 * Implements transaction-related operations for the knowledge graph.
 * Handles transaction lifecycle, rollback actions, and maintaining transaction state.
 */
export declare class TransactionManager extends IManager implements ITransactionManager {
    private graph;
    private rollbackActions;
    private inTransaction;
    constructor(storage: IStorage);
    /**
     * Begins a new transaction.
     * @throws Error if a transaction is already in progress
     */
    beginTransaction(): Promise<void>;
    /**
     * Adds a rollback action to be executed if the transaction is rolled back.
     * @throws Error if no transaction is in progress
     */
    addRollbackAction(action: () => Promise<void>, description: string): Promise<void>;
    /**
     * Commits the current transaction.
     * @throws Error if no transaction is in progress
     */
    commit(): Promise<void>;
    /**
     * Rolls back the current transaction, executing all rollback actions in reverse order.
     * @throws Error if no transaction is in progress
     */
    rollback(): Promise<void>;
    /**
     * Gets the current graph state within the transaction.
     */
    getCurrentGraph(): Graph;
    /**
     * Checks if a transaction is currently in progress.
     */
    isInTransaction(): boolean;
    /**
     * Executes an operation within a transaction, handling commit and rollback automatically.
     */
    withTransaction<T>(operation: () => Promise<T>): Promise<T>;
}
