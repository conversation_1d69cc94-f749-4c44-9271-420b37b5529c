import type { Tool, ToolResponse } from '../../../shared/index.js';
import type { ApplicationManager } from '../../../application/index.js';
/**
 * Central registry for all tools (both static and dynamic)
 */
export declare class ToolsRegistry {
    private static instance;
    private initialized;
    private tools;
    private knowledgeGraphManager;
    private constructor();
    /**
     * Gets the singleton instance of ToolsRegistry
     */
    static getInstance(): ToolsRegistry;
    /**
     * Initializes the registry with both static and dynamic tools
     */
    initialize(knowledgeGraphManager: ApplicationManager): Promise<void>;
    /**
     * Gets a specific tool by name
     */
    getTool(name: string): Tool | undefined;
    /**
     * Gets all registered tools
     */
    getAllTools(): Tool[];
    /**
     * Handles a tool call by delegating to the appropriate handler
     */
    handleToolCall(toolName: string, args: Record<string, any>): Promise<ToolResponse>;
    /**
     * Checks if a specific tool exists
     */
    hasTool(name: string): boolean;
    /**
     * Ensures the registry is initialized before use
     */
    private ensureInitialized;
}
/**
 * Singleton instance of the ToolsRegistry
 */
export declare const toolsRegistry: ToolsRegistry;
/**
 * Re-export types that might be needed by consumers
 */
export type { Tool, ToolResponse, ApplicationManager };
