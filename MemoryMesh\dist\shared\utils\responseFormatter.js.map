{"version": 3, "file": "responseFormatter.js", "sourceRoot": "", "sources": ["../../../src/shared/utils/responseFormatter.ts"], "names": [], "mappings": "AAAA,iCAAiC;AAUjC;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAAU,EACI,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW,GAAG,EAAE,EACK;IACjE,mCAAmC;IACnC,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,WAAW,EAAE,EAAC,CAAC,CAAC;IACvE,CAAC;IAED,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,kDAAkD;IAClD,OAAO;QACH,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,kCAAkC,EAAC,CAAC;QAClG,OAAO,EAAE,KAAK;KACjB,CAAC;AACN,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,EACI,SAAS,EACT,KAAK,EACL,OAAO,EACP,WAAW,GAAG,EAAE,EAChB,aAAa,GAAG,EAAE,EACH;IAC/C,MAAM,OAAO,GAAG;QACZ,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,SAAS,KAAK,KAAK,EAAE,EAAC;KAC9D,CAAC;IAEF,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC;IACvF,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACtF,CAAC;IAED,kDAAkD;IAClD,OAAO;QACH,OAAO;QACP,OAAO,EAAE,IAAI;KAChB,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAI,EACI,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACgB;IAC/D,MAAM,UAAU,GAAe;QAC3B,OAAO,EAAE,IAAI;QACb,OAAO,EAAE;YACL;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,uBAAuB,SAAS,KAAK,SAAS,CAAC,MAAM,eAAe,MAAM,CAAC,MAAM,SAAS;aACnG;YACD;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;aAC9C;SACJ;QACD,IAAI,EAAE;YACF,cAAc,EAAE,SAAS;YACzB,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7B,IAAI;gBACJ,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,eAAe;aACnD,CAAC,CAAC;SACN;QACD,WAAW,EAAE;YACT,uCAAuC;YACvC,kDAAkD;YAClD,sCAAsC;SACzC;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACtC,CAAC;IAEF,OAAO,EAAC,UAAU,EAAC,CAAC;AACxB,CAAC"}