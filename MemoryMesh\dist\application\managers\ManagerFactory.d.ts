import type { IStorage } from '../../infrastructure/index.js';
import type { INodeManager, IEdgeManager, IMetadataManager, ISearchManager, ITransactionManager } from '../index.js';
/**
 * Factory class responsible for creating instances of various manager classes
 * used in the knowledge graph. Ensures consistent initialization and configuration
 * of all manager instances.
 */
export declare class ManagerFactory {
    private static instances;
    /**
     * Creates or returns an existing instance of NodeManager
     */
    static getNodeManager(storage: IStorage): INodeManager;
    /**
     * Creates or returns an existing instance of EdgeManager
     */
    static getEdgeManager(storage: IStorage): IEdgeManager;
    /**
     * Creates or returns an existing instance of MetadataManager
     */
    static getMetadataManager(storage: IStorage): IMetadataManager;
    /**
     * Creates or returns an existing instance of SearchManager
     */
    static getSearchManager(storage: IStorage): ISearchManager;
    /**
     * Creates or returns an existing instance of TransactionManager
     */
    static getTransactionManager(storage: IStorage): ITransactionManager;
    /**
     * Creates all manager instances at once
     */
    static getAllManagers(storage: IStorage): {
        nodeManager: INodeManager;
        edgeManager: IEdgeManager;
        metadataManager: IMetadataManager;
        searchManager: ISearchManager;
        transactionManager: ITransactionManager;
    };
    /**
     * Clears all cached manager instances
     */
    static clearInstances(): void;
}
