// src/application/managers/base/BaseManager.ts
import { JsonLineStorage } from '../../../infrastructure/index.js';
import { ManagerFactory } from '../../index.js';
/**
 * Base class that handles initialization and common functionality
 */
export class BaseManager {
    storage;
    constructor(storage = new JsonLineStorage()) {
        this.storage = storage;
    }
    createManagers() {
        return {
            nodeManager: ManagerFactory.getNodeManager(this.storage),
            edgeManager: ManagerFactory.getEdgeManager(this.storage),
            metadataManager: ManagerFactory.getMetadataManager(this.storage),
            searchManager: ManagerFactory.getSearchManager(this.storage),
            transactionManager: ManagerFactory.getTransactionManager(this.storage)
        };
    }
}
//# sourceMappingURL=BaseManager.js.map