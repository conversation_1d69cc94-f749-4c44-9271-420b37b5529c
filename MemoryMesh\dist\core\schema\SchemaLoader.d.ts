import { SchemaBuilder } from './SchemaBuilder.js';
interface RawSchemaProperty {
    type: string;
    description: string;
    required?: boolean;
    enum?: string[];
    relationship?: {
        edgeType: string;
        description: string;
        nodeType?: string;
    };
}
interface RawSchema {
    name: string;
    description: string;
    properties: Record<string, RawSchemaProperty>;
    additionalProperties?: boolean;
}
/**
 * Responsible for loading and converting schema definitions from JSON files into SchemaBuilder instances.
 */
export declare class SchemaLoader {
    /**
     * Loads a specific schema by name.
     */
    static loadSchema(schemaName: string): Promise<SchemaBuilder>;
    /**
     * Converts a JSON schema object into a SchemaBuilder instance.
     */
    static convertToSchemaBuilder(schema: RawSchema): SchemaBuilder;
    /**
     * Loads all schema files from the schemas directory.
     */
    static loadAllSchemas(): Promise<Record<string, SchemaBuilder>>;
    /**
     * Validates a schema definition.
     * @throws {Error} If the schema is invalid
     */
    private static validateSchema;
}
export {};
