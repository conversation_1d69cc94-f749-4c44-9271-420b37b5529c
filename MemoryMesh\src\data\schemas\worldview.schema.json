{"name": "add_worldview", "description": "Add worldview and setting framework including time period, world rules, magic systems, and fundamental world mechanics. Used by AI Worker #1.", "properties": {"name": {"type": "string", "description": "Name of the world or setting", "required": true}, "worldType": {"type": "string", "description": "Type of world setting", "required": true, "enum": ["Contemporary Real World", "Historical Real World", "Alternate History", "Fantasy World", "Science Fiction", "Urban Fantasy", "Dystopian", "Post-Apocalyptic", "Parallel Universe", "Other"]}, "timePeriod": {"type": "string", "description": "Time period or era of the story", "required": true}, "geographicalScope": {"type": "string", "description": "Geographical scope of the story", "required": true, "enum": ["Single City", "Multiple Cities", "Single Country", "Multiple Countries", "Single Continent", "Global", "Interplanetary", "Other Dimension"]}, "technologyLevel": {"type": "string", "description": "Level of technology in the world", "required": true, "enum": ["Stone Age", "Medieval", "Renaissance", "Industrial", "Modern", "Near Future", "Far Future", "Mixed Technology"]}, "magicSystem": {"type": "string", "description": "Magic or supernatural system (if applicable)", "required": false}, "worldRules": {"type": "array", "description": "Fundamental rules that govern this world", "required": true}, "socialStructure": {"type": "string", "description": "Overall social and political structure", "required": false}, "culturalElements": {"type": "array", "description": "Important cultural aspects and customs", "required": false}, "languages": {"type": "array", "description": "Languages spoken in this world", "required": false}, "religions": {"type": "array", "description": "Religious or belief systems", "required": false}, "economy": {"type": "string", "description": "Economic system and currency", "required": false}, "conflicts": {"type": "array", "description": "Major conflicts or tensions in the world", "required": false}, "uniqueFeatures": {"type": "array", "description": "Unique or distinctive features of this world", "required": true}, "limitations": {"type": "array", "description": "Limitations or constraints within this world", "required": false}, "history": {"type": "string", "description": "Brief history of the world relevant to the story", "required": false}, "currentEvents": {"type": "array", "description": "Current events happening in the world during the story", "required": false}, "relatedNovel": {"type": "string", "description": "The novel this worldview belongs to", "required": true, "relationship": {"edgeType": "worldview_of", "description": "Worldview belongs to novel"}}, "status": {"type": "string", "description": "Development status of the worldview", "required": true, "enum": ["Concept", "Basic Framework", "Detailed", "Complete", "Needs Revision"]}, "notes": {"type": "array", "description": "Additional worldbuilding notes", "required": false}}, "additionalProperties": true}