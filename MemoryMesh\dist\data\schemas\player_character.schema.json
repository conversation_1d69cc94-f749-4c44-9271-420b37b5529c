{"name": "add_player_character", "description": "Add a new Player Character to the knowledge graph", "properties": {"name": {"type": "string", "description": "Player character's name", "required": true}, "age": {"type": "string", "description": "Player character's age", "required": true}, "gender": {"type": "string", "description": "Player character's gender", "required": true}, "occupation": {"type": "string", "description": "Player character's occupation", "required": true}, "status": {"type": "string", "description": "Player character's current status", "required": true}, "race": {"type": "string", "description": "Player character's race", "required": false}, "description": {"type": "string", "description": "A detailed description of the player character", "required": false}, "background": {"type": "string", "description": "The background story of the player character", "required": false}, "equipment": {"type": "array", "description": "List of equipment items associated with the player character", "required": false}}, "additionalProperties": true}