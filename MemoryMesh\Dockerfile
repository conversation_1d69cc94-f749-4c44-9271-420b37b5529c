# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use a Node.js image with Node.js 18
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm install --ignore-scripts

# Copy source files
COPY . .

# Build the TypeScript project
RUN npm run build

# Use a minimal Node.js runtime for the final image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy the built files from the builder stage
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package.json /app/package-lock.json ./

# Install only production dependencies
RUN npm ci --omit=dev

# Expose necessary ports (if applicable)
# EXPOSE 3000  # Example port, adjust as necessary

# Start the server
CMD ["node", "dist/index.js"]
