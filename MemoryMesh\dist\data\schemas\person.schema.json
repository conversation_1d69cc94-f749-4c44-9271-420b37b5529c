{"name": "add_person", "description": "Add a new person/individual to the knowledge graph. Suitable for employees, contacts, characters, or any human entity.", "properties": {"name": {"type": "string", "description": "Person's full name or identifier", "required": true}, "role": {"type": "string", "description": "Person's primary role, job title, or function", "required": true}, "status": {"type": "string", "description": "Current status of the person", "required": true, "enum": ["Active", "Inactive", "Pending", "Archived", "Unknown"]}, "currentLocation": {"type": "string", "description": "Current location or workplace of the person", "required": false, "relationship": {"edgeType": "located_at", "description": "Person's current location"}}, "description": {"type": "string", "description": "Detailed description of the person", "required": false}, "contactInfo": {"type": "array", "description": "Contact information (email, phone, etc.)", "required": false}, "skills": {"type": "array", "description": "Skills, competencies, or abilities", "required": false}, "department": {"type": "string", "description": "Department, team, or group affiliation", "required": false, "relationship": {"edgeType": "member_of", "description": "Person's department or team"}}, "supervisor": {"type": "string", "description": "Direct supervisor or manager", "required": false, "relationship": {"edgeType": "reports_to", "description": "Reporting relationship"}}, "startDate": {"type": "string", "description": "Start date or joining date", "required": false}, "notes": {"type": "array", "description": "Additional notes or observations", "required": false}, "tags": {"type": "array", "description": "Tags or categories for classification", "required": false}, "priority": {"type": "string", "description": "Priority level or importance", "required": false, "enum": ["High", "Medium", "Low"]}}, "additionalProperties": true}