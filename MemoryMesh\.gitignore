# Node.js
node_modules/
npm-debug.log*
yarn-error.log*

# TypeScript
dist/

# System Files (macOS)
.DS_Store

# IDE-specific files
.idea/

# Log files
*.log
*.log.*

# Temporary files
*.tmp
*.temp

# OS generated files
Thumbs.db
ehthumbs.db

# dotenv
.env

# Ignore cache and log files created by npm
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Coverage reports
coverage/
.nyc_output/

# Ignore the memory file
src/data/memory.json

# Ignore schema changes
scr/data/schemas/*.json