import { BaseManager } from '../index.js';
import type { MetadataAddition, MetadataDeletion, MetadataResult, Node, Edge } from '../../core/index.js';
import type { EdgeFilter, GetEdgesResult } from '../../shared/index.js';
import type { IStorage } from '../../infrastructure/index.js';
/**
 * Handles graph-specific operations (nodes, edges, metadata)
 */
export declare class GraphManager extends BaseManager {
    private readonly graphOperations;
    constructor(storage?: IStorage);
    addNodes(nodes: Node[]): Promise<Node[]>;
    updateNodes(nodes: Partial<Node>[]): Promise<Node[]>;
    deleteNodes(nodeNames: string[]): Promise<void>;
    addEdges(edges: Edge[]): Promise<Edge[]>;
    updateEdges(edges: Edge[]): Promise<Edge[]>;
    deleteEdges(edges: Edge[]): Promise<void>;
    getEdges(filter?: EdgeFilter): Promise<GetEdgesResult>;
    addMetadata(metadata: MetadataAddition[]): Promise<MetadataResult[]>;
    deleteMetadata(deletions: MetadataDeletion[]): Promise<void>;
}
