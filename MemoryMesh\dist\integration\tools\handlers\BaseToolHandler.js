// src/tools/handlers/BaseToolHandler.ts
import { formatToolError } from '../../../shared/index.js';
export class BaseToolHandler {
    knowledgeGraphManager;
    constructor(knowledgeGraphManager) {
        this.knowledgeGraphManager = knowledgeGraphManager;
    }
    validateArguments(args) {
        if (!args) {
            throw new Error("Tool arguments are required");
        }
    }
    handleError(name, error) {
        console.error(`Error in ${name}:`, error);
        return formatToolError({
            operation: name,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            context: { toolName: name },
            suggestions: ["Examine the tool input parameters for correctness.", "Verify that the requested operation is supported."],
            recoverySteps: ["Adjust the input parameters based on the schema definition."]
        });
    }
}
//# sourceMappingURL=BaseToolHandler.js.map