{"version": 3, "file": "graphSimplifier.js", "sourceRoot": "", "sources": ["../../../src/shared/utils/graphSimplifier.ts"], "names": [], "mappings": "AAAA,sCAAsC;AAmCtC;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,KAAY;IACtC,2BAA2B;IAC3B,MAAM,eAAe,GAAqB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/D,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;KAC1B,CAAC,CAAC,CAAC;IAEJ,eAAe;IACf,MAAM,eAAe,GAAqB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/D,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,EAAE,EAAE,IAAI,CAAC,EAAE;QACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;KAC1B,CAAC,CAAC,CAAC;IAEJ,SAAS;IACT,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC9E,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAE9E,OAAO;QACH,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,eAAe;QACtB,OAAO,EAAE;YACL,UAAU,EAAE,eAAe,CAAC,MAAM;YAClC,UAAU,EAAE,eAAe,CAAC,MAAM;YAClC,SAAS;YACT,SAAS;SACZ;KACJ,CAAC;AACN,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,2BAA2B,CAAC,eAAgC;IACxE,MAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,SAAS;IACT,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3B,KAAK,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAC1D,KAAK,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAC1D,KAAK,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpE,KAAK,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEf,YAAY;IACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3B,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEhD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEf,YAAY;IACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3B,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEhD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QAC1D,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC3B,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB,CAAC,aAAoB,EAAE,eAAgC;IAM5F,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;IAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;IAC9D,MAAM,gBAAgB,GAAG,CAAC,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;IAChF,MAAM,UAAU,GAAG,YAAY,GAAG,cAAc,CAAC;IAEjD,OAAO;QACH,YAAY;QACZ,cAAc;QACd,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;QAC1D,UAAU;KACb,CAAC;AACN,CAAC"}