import { EventEmitter } from '../../infrastructure/index.js';
import type { ITransactionManager } from '../index.js';
import type { Graph } from '../../core/index.js';
export declare class TransactionOperations extends EventEmitter {
    private transactionManager;
    constructor(transactionManager: ITransactionManager);
    beginTransaction(): Promise<void>;
    commit(): Promise<void>;
    rollback(): Promise<void>;
    withTransaction<T>(operation: () => Promise<T>): Promise<T>;
    addRollbackAction(action: () => Promise<void>, description: string): Promise<void>;
    isInTransaction(): boolean;
    getCurrentGraph(): Graph;
}
