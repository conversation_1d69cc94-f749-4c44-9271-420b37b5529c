// src/core/operations/GraphOperations.ts
import { EventEmitter } from '../../infrastructure/index.js';
export class GraphOperations extends EventEmitter {
    nodeManager;
    edgeManager;
    metadataManager;
    constructor(nodeManager, edgeManager, metadataManager) {
        super();
        this.nodeManager = nodeManager;
        this.edgeManager = edgeManager;
        this.metadataManager = metadataManager;
    }
    async addNodes(nodes) {
        this.emit('beforeAddNodes', { nodes });
        const result = await this.nodeManager.addNodes(nodes);
        this.emit('afterAddNodes', { nodes: result });
        return result;
    }
    async updateNodes(nodes) {
        this.emit('beforeUpdateNodes', { nodes });
        const result = await this.nodeManager.updateNodes(nodes);
        this.emit('afterUpdateNodes', { nodes: result });
        return result;
    }
    async deleteNodes(nodeNames) {
        this.emit('beforeDeleteNodes', { nodeNames });
        await this.nodeManager.deleteNodes(nodeNames);
        this.emit('afterDeleteNodes', { nodeNames });
    }
    async addEdges(edges) {
        this.emit('beforeAddEdges', { edges });
        const result = await this.edgeManager.addEdges(edges);
        this.emit('afterAddEdges', { edges: result });
        return result;
    }
    async updateEdges(edges) {
        this.emit('beforeUpdateEdges', { edges });
        const result = await this.edgeManager.updateEdges(edges);
        this.emit('afterUpdateEdges', { edges: result });
        return result;
    }
    async deleteEdges(edges) {
        this.emit('beforeDeleteEdges', { edges });
        await this.edgeManager.deleteEdges(edges);
        this.emit('afterDeleteEdges', { edges });
    }
    async getEdges(filter) {
        const edges = await this.edgeManager.getEdges(filter);
        return { edges };
    }
    async addMetadata(metadata) {
        this.emit('beforeAddMetadata', { metadata });
        const result = await this.metadataManager.addMetadata(metadata);
        this.emit('afterAddMetadata', { results: result });
        return result;
    }
    async deleteMetadata(deletions) {
        this.emit('beforeDeleteMetadata', { deletions });
        await this.metadataManager.deleteMetadata(deletions);
        this.emit('afterDeleteMetadata', { deletions });
    }
}
//# sourceMappingURL=GraphOperations.js.map