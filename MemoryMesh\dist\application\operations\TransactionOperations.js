// src/core/operations/TransactionOperations.ts
import { EventEmitter } from '../../infrastructure/index.js';
export class TransactionOperations extends EventEmitter {
    transactionManager;
    constructor(transactionManager) {
        super();
        this.transactionManager = transactionManager;
    }
    async beginTransaction() {
        this.emit('beforeBeginTransaction', {});
        await this.transactionManager.beginTransaction();
        this.emit('afterBeginTransaction', {});
    }
    async commit() {
        this.emit('beforeCommit', {});
        await this.transactionManager.commit();
        this.emit('afterCommit', {});
    }
    async rollback() {
        this.emit('beforeRollback', {});
        await this.transactionManager.rollback();
        this.emit('afterRollback', {});
    }
    async withTransaction(operation) {
        await this.beginTransaction();
        try {
            const result = await operation();
            await this.commit();
            return result;
        }
        catch (error) {
            await this.rollback();
            throw error;
        }
    }
    async addRollbackAction(action, description) {
        await this.transactionManager.addRollbackAction(action, description);
    }
    isInTransaction() {
        return this.transactionManager.isInTransaction();
    }
    getCurrentGraph() {
        return this.transactionManager.getCurrentGraph();
    }
}
//# sourceMappingURL=TransactionOperations.js.map