import { MetadataEntry, Metadata } from './Metadata.js';
export declare class MetadataProcessor {
    /**
     * Converts a raw metadata string to a structured entry
     */
    static parseMetadataEntry(entry: string): MetadataEntry;
    /**
     * Formats a metadata entry into a string
     */
    static formatMetadataEntry(key: string, value: string | string[] | unknown): string;
    /**
     * Processes and validates metadata entries
     */
    static validateMetadata(metadata: Metadata): boolean;
    /**
     * Merges multiple metadata arrays, removing duplicates
     */
    static mergeMetadata(...metadataArrays: Metadata[]): Metadata;
    /**
     * Filters metadata entries by key
     */
    static filterByKey(metadata: Metadata, key: string): Metadata;
    /**
     * Extracts value for a specific metadata key
     */
    static getValue(metadata: Metadata, key: string): string | null;
    /**
     * Creates a metadata entry map for efficient lookup
     */
    static createMetadataMap(metadata: Metadata): Map<string, string>;
}
