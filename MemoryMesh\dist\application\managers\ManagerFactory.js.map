{"version": 3, "file": "ManagerFactory.js", "sourceRoot": "", "sources": ["../../../src/application/managers/ManagerFactory.ts"], "names": [], "mappings": "AAAA,sCAAsC;AAEtC,OAAO,EACH,WAAW,EACX,WAAW,EACX,eAAe,EACf,aAAa,EACb,kBAAkB,EACrB,MAAM,uBAAuB,CAAC;AAU/B;;;;GAIG;AACH,MAAM,OAAO,cAAc;IACf,MAAM,CAAC,SAAS,GAMpB,EAAE,CAAC;IAEP;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAiB;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAiB;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,OAAiB;QACvC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,OAAiB;QACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,OAAiB;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAiB;QACnC,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACzC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACzC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACjD,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC7C,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;SAC1D,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc;QACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACxB,CAAC"}