// src/core/managers/implementations/TransactionManager.ts
import { IManager } from './interfaces/IManager.js';
/**
 * Implements transaction-related operations for the knowledge graph.
 * Handles transaction lifecycle, rollback actions, and maintaining transaction state.
 */
export class TransactionManager extends IManager {
    graph;
    rollbackActions;
    inTransaction;
    constructor(storage) {
        super(storage);
        this.graph = { nodes: [], edges: [] };
        this.rollbackActions = [];
        this.inTransaction = false;
    }
    /**
     * Begins a new transaction.
     * @throws Error if a transaction is already in progress
     */
    async beginTransaction() {
        if (this.inTransaction) {
            throw new Error('Transaction already in progress');
        }
        this.emit('beforeBeginTransaction', {});
        try {
            // Load current state
            this.graph = await this.storage.loadGraph();
            this.rollbackActions = [];
            this.inTransaction = true;
            this.emit('afterBeginTransaction', {});
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'Unknown error occurred';
            throw new Error(`Failed to begin transaction: ${message}`);
        }
    }
    /**
     * Adds a rollback action to be executed if the transaction is rolled back.
     * @throws Error if no transaction is in progress
     */
    async addRollbackAction(action, description) {
        if (!this.inTransaction) {
            throw new Error('No transaction in progress');
        }
        this.rollbackActions.push({ action, description });
    }
    /**
     * Commits the current transaction.
     * @throws Error if no transaction is in progress
     */
    async commit() {
        if (!this.inTransaction) {
            throw new Error('No transaction to commit');
        }
        this.emit('beforeCommit', {});
        try {
            // Clear the transaction state
            this.rollbackActions = [];
            this.inTransaction = false;
            this.emit('afterCommit', {});
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'Unknown error occurred';
            throw new Error(`Failed to commit transaction: ${message}`);
        }
    }
    /**
     * Rolls back the current transaction, executing all rollback actions in reverse order.
     * @throws Error if no transaction is in progress
     */
    async rollback() {
        if (!this.inTransaction) {
            throw new Error('No transaction to rollback');
        }
        this.emit('beforeRollback', { actions: this.rollbackActions });
        try {
            // Execute rollback actions in reverse order
            for (const { action, description } of this.rollbackActions.reverse()) {
                try {
                    await action();
                }
                catch (error) {
                    console.error(`Error during rollback action (${description}):`, error);
                    // Continue with other rollbacks even if one fails
                }
            }
            // Clear the transaction state
            this.rollbackActions = [];
            this.inTransaction = false;
            this.emit('afterRollback', {});
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'Unknown error occurred';
            throw new Error(`Failed to rollback transaction: ${message}`);
        }
    }
    /**
     * Gets the current graph state within the transaction.
     */
    getCurrentGraph() {
        return this.graph;
    }
    /**
     * Checks if a transaction is currently in progress.
     */
    isInTransaction() {
        return this.inTransaction;
    }
    /**
     * Executes an operation within a transaction, handling commit and rollback automatically.
     */
    async withTransaction(operation) {
        await this.beginTransaction();
        try {
            const result = await operation();
            await this.commit();
            return result;
        }
        catch (error) {
            await this.rollback();
            throw error;
        }
    }
}
//# sourceMappingURL=TransactionManager.js.map