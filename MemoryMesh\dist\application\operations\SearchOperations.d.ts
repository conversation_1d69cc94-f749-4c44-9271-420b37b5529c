import { EventEmitter } from '../../infrastructure/index.js';
import type { ISearchManager } from '../index.js';
import type { OpenNodesResult } from '../../shared/index.js';
import type { Graph } from '../../core/index.js';
export declare class SearchOperations extends EventEmitter {
    private searchManager;
    constructor(searchManager: ISearchManager);
    searchNodes(query: string): Promise<OpenNodesResult>;
    openNodes(names: string[]): Promise<OpenNodesResult>;
    readGraph(): Promise<Graph>;
}
