{"name": "add_theme", "description": "Add thematic elements, motifs, and stylistic choices that define the novel's deeper meaning and artistic direction. Used by AI Worker #1.", "properties": {"name": {"type": "string", "description": "Theme name or identifier", "required": true}, "type": {"type": "string", "description": "Type of thematic element", "required": true, "enum": ["Core Theme", "Surface Theme", "<PERSON><PERSON><PERSON>", "Symbol", "Stylistic Choice", "Narrative Device"]}, "description": {"type": "string", "description": "Detailed description of the theme or element", "required": true}, "importance": {"type": "string", "description": "Importance level in the overall narrative", "required": true, "enum": ["Primary", "Secondary", "Supporting", "Subtle"]}, "manifestations": {"type": "array", "description": "How this theme manifests in the story", "required": true}, "relatedCharacters": {"type": "array", "description": "Characters most connected to this theme", "required": false, "relationship": {"edgeType": "embodies_theme", "description": "Character embodies theme"}}, "relatedEvents": {"type": "array", "description": "Story events that highlight this theme", "required": false, "relationship": {"edgeType": "demonstrates_theme", "description": "Event demonstrates theme"}}, "symbols": {"type": "array", "description": "Symbols or objects that represent this theme", "required": false}, "opposingForces": {"type": "array", "description": "Themes or elements that oppose or contrast with this one", "required": false}, "development": {"type": "string", "description": "How this theme develops throughout the story", "required": false}, "resolution": {"type": "string", "description": "How this theme is resolved or concluded", "required": false}, "literaryDevices": {"type": "array", "description": "Literary devices used to convey this theme", "required": false}, "culturalContext": {"type": "string", "description": "Cultural or historical context relevant to this theme", "required": false}, "personalRelevance": {"type": "string", "description": "Why this theme is personally meaningful or relevant", "required": false}, "targetImpact": {"type": "string", "description": "Intended impact on readers", "required": false}, "relatedNovel": {"type": "string", "description": "The novel this theme belongs to", "required": true, "relationship": {"edgeType": "theme_of", "description": "Theme belongs to novel"}}, "status": {"type": "string", "description": "Development status of the theme", "required": true, "enum": ["Concept", "Developing", "Established", "Integrated", "Complete"]}, "notes": {"type": "array", "description": "Additional notes about the theme", "required": false}}, "additionalProperties": true}