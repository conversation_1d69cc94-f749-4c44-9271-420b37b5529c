{"name": "add_skills", "description": "Defines list of skills or abilities a character can possess.", "properties": {"name": {"type": "string", "description": "[Entity]_abilities", "required": true}, "owner": {"type": "string", "description": "The entity or character that owns these skills.", "required": true, "relationship": {"edgeType": "possesses", "description": "The relationship between the skill and its owner."}}}, "additionalProperties": true}