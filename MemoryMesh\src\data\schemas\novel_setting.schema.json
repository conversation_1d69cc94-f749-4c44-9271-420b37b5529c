{"name": "add_novel_setting", "description": "Add basic novel settings including genre, style, core concepts, and fundamental framework. Used by AI Worker #1 for establishing the foundation.", "properties": {"name": {"type": "string", "description": "Novel title or project name", "required": true}, "genre": {"type": "string", "description": "Primary genre of the novel", "required": true, "enum": ["Romance", "Fantasy", "Science Fiction", "Mystery", "Thriller", "Historical", "Contemporary", "Urban Fantasy", "Paranormal Romance", "Young Adult", "Literary Fiction", "Other"]}, "subGenres": {"type": "array", "description": "Secondary genres or sub-categories", "required": false}, "writingStyle": {"type": "string", "description": "Overall writing style and tone", "required": true, "enum": ["First Person", "Third Person Limited", "Third Person Omniscient", "Multiple POV", "Epistolary", "Stream of Consciousness"]}, "targetAudience": {"type": "string", "description": "Target reader demographic", "required": true, "enum": ["Young Adult", "New Adult", "Adult", "Middle Grade", "General Fiction"]}, "coreTheme": {"type": "string", "description": "Central theme or message of the novel", "required": true}, "surfaceTheme": {"type": "string", "description": "Surface-level theme or apparent story focus", "required": true}, "mood": {"type": "string", "description": "Overall emotional tone and atmosphere", "required": true, "enum": ["Light and Humorous", "Dark and Serious", "Romantic and Dreamy", "Suspenseful and Tense", "Melancholic and Reflective", "Uplifting and Hopeful", "Mysterious and Intriguing"]}, "estimatedLength": {"type": "string", "description": "Planned length of the novel", "required": false, "enum": ["Novella (20,000-50,000 words)", "Short Novel (50,000-80,000 words)", "Standard Novel (80,000-120,000 words)", "Long Novel (120,000+ words)"]}, "plannedChapters": {"type": "string", "description": "Estimated number of chapters", "required": false}, "contentWarnings": {"type": "array", "description": "Content warnings or sensitive topics", "required": false}, "inspirations": {"type": "array", "description": "Books, movies, or other works that inspired this novel", "required": false}, "uniqueElements": {"type": "array", "description": "Unique or distinctive elements that set this novel apart", "required": false}, "status": {"type": "string", "description": "Current development status", "required": true, "enum": ["Concept", "Planning", "Outlining", "Writing", "Editing", "Complete"]}, "notes": {"type": "array", "description": "Additional notes and observations", "required": false}}, "additionalProperties": true}