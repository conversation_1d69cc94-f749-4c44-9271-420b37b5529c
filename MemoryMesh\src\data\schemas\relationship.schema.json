{"name": "add_relationship", "description": "Add a new relationship, connection, or association between entities in the knowledge graph. Suitable for partnerships, collaborations, or any meaningful connection.", "properties": {"name": {"type": "string", "description": "Relationship name or identifier", "required": true}, "type": {"type": "string", "description": "Type of relationship", "required": true, "enum": ["Partnership", "Collaboration", "Mentorship", "Reporting", "Friendship", "Business", "Family", "Professional", "Contractual", "Other"]}, "description": {"type": "string", "description": "Detailed description of the relationship", "required": true}, "status": {"type": "string", "description": "Current status of the relationship", "required": true, "enum": ["Active", "Inactive", "Pending", "Terminated", "Suspended"]}, "primaryEntity": {"type": "string", "description": "Primary entity in the relationship", "required": true, "relationship": {"edgeType": "involves", "description": "Primary relationship participant"}}, "secondaryEntity": {"type": "string", "description": "Secondary entity in the relationship", "required": true, "relationship": {"edgeType": "involves", "description": "Secondary relationship participant"}}, "startDate": {"type": "string", "description": "When the relationship began", "required": false}, "endDate": {"type": "string", "description": "When the relationship ended (if applicable)", "required": false}, "strength": {"type": "string", "description": "Strength or intensity of the relationship", "required": false, "enum": ["Strong", "Medium", "Weak", "Variable"]}, "direction": {"type": "string", "description": "Direction of the relationship", "required": false, "enum": ["Bidirectional", "Unidirectional", "Hierarchical"]}, "context": {"type": "string", "description": "Context or setting of the relationship", "required": false}, "benefits": {"type": "array", "description": "Benefits or advantages of the relationship", "required": false}, "challenges": {"type": "array", "description": "Challenges or issues in the relationship", "required": false}, "keyContacts": {"type": "array", "description": "Key contact persons for this relationship", "required": false, "relationship": {"edgeType": "contact_for", "description": "Relationship contacts"}}, "agreements": {"type": "array", "description": "Agreements or contracts related to the relationship", "required": false}, "notes": {"type": "array", "description": "Additional notes or observations", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}}, "additionalProperties": true}