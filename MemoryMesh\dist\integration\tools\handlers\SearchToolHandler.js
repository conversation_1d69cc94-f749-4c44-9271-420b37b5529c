// src/tools/handlers/SearchToolHandler.ts
import { BaseToolHandler } from './BaseToolHandler.js';
import { formatToolResponse, formatToolError } from '../../../shared/index.js';
import { simplifyGraph, formatSimplifiedGraphAsText, calculateCompressionRatio } from '../../../shared/utils/graphSimplifier.js';
export class SearchToolHandler extends BaseToolHandler {
    async handleTool(name, args) {
        try {
            this.validateArguments(args);
            switch (name) {
                case "read_graph":
                    const graph = await this.knowledgeGraphManager.readGraph();
                    // 簡化圖譜數據以減少 token 消耗和提高性能
                    const simplifiedGraph = simplifyGraph(graph);
                    const textFormat = formatSimplifiedGraphAsText(simplifiedGraph);
                    const compressionStats = calculateCompressionRatio(graph, simplifiedGraph);
                    return formatToolResponse({
                        data: simplifiedGraph,
                        message: textFormat,
                        actionTaken: `Read simplified knowledge graph (${compressionStats.compressionRatio}% size reduction, saved ${compressionStats.savedBytes} bytes)`
                    });
                case "search_nodes":
                    const searchResults = await this.knowledgeGraphManager.searchNodes(args.query);
                    return formatToolResponse({
                        data: searchResults,
                        actionTaken: `Searched nodes with query: ${args.query}`
                    });
                case "open_nodes":
                    const nodes = await this.knowledgeGraphManager.openNodes(args.names);
                    return formatToolResponse({
                        data: nodes,
                        actionTaken: `Retrieved nodes: ${args.names.join(', ')}`
                    });
                default:
                    throw new Error(`Unknown search operation: ${name}`);
            }
        }
        catch (error) {
            return formatToolError({
                operation: name,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                context: { args },
                suggestions: [
                    "Check node names exist",
                    "Verify search query format"
                ],
                recoverySteps: [
                    "Try with different node names",
                    "Adjust search query parameters"
                ]
            });
        }
    }
}
//# sourceMappingURL=SearchToolHandler.js.map