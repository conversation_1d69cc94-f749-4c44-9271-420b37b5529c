// src/core/operations/SearchOperations.ts
import { EventEmitter } from '../../infrastructure/index.js';
export class SearchOperations extends EventEmitter {
    searchManager;
    constructor(searchManager) {
        super();
        this.searchManager = searchManager;
    }
    async searchNodes(query) {
        this.emit('beforeSearch', { query });
        const result = await this.searchManager.searchNodes(query);
        this.emit('afterSearch', result);
        return result;
    }
    async openNodes(names) {
        this.emit('beforeOpenNodes', { names });
        const result = await this.searchManager.openNodes(names);
        this.emit('afterOpenNodes', result);
        return result;
    }
    async readGraph() {
        this.emit('beforeReadGraph', {});
        const result = await this.searchManager.readGraph();
        this.emit('afterReadGraph', result);
        return result;
    }
}
//# sourceMappingURL=SearchOperations.js.map