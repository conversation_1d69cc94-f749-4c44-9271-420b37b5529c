{"name": "add_character_supporting", "description": "Add supporting characters including love interests, antagonists, side characters, and background characters. Used by AI Worker #2 for world population.", "properties": {"name": {"type": "string", "description": "Character's full name", "required": true}, "role": {"type": "string", "description": "Character's role in the story", "required": true, "enum": ["Love Interest", "Antagonist", "Best Friend", "Family Member", "Mentor", "Rival", "Side Character", "Background Character", "Comic Relief", "Confidant", "Other"]}, "importance": {"type": "string", "description": "Character's importance to the story", "required": true, "enum": ["Major Supporting", "Minor Supporting", "Recurring", "<PERSON>o", "Background"]}, "age": {"type": "string", "description": "Character's age", "required": false}, "gender": {"type": "string", "description": "Character's gender identity", "required": false}, "physicalDescription": {"type": "array", "description": "Key physical appearance details", "required": false}, "personality": {"type": "array", "description": "Core personality traits", "required": true}, "background": {"type": "string", "description": "Character's background and history", "required": false}, "occupation": {"type": "string", "description": "Character's job or primary activity", "required": false}, "motivations": {"type": "array", "description": "Character's goals and motivations", "required": false}, "relationshipToMain": {"type": "string", "description": "Relationship to main character(s)", "required": true, "relationship": {"edgeType": "has_relationship_with", "description": "Relationship with main character"}}, "characterFunction": {"type": "string", "description": "What function this character serves in the story", "required": true}, "firstAppearance": {"type": "string", "description": "When/where the character first appears", "required": false}, "keyScenes": {"type": "array", "description": "Important scenes featuring this character", "required": false}, "characterArc": {"type": "string", "description": "Character's development arc (if any)", "required": false}, "secrets": {"type": "array", "description": "Hidden aspects about the character", "required": false}, "speechPattern": {"type": "string", "description": "How the character speaks", "required": false}, "currentLocation": {"type": "string", "description": "Character's current or primary location", "required": false, "relationship": {"edgeType": "located_at", "description": "Character location"}}, "affiliations": {"type": "array", "description": "Organizations or groups the character belongs to", "required": false, "relationship": {"edgeType": "member_of", "description": "Character affiliations"}}, "relationships": {"type": "array", "description": "Relationships with other characters", "required": false, "relationship": {"edgeType": "knows", "description": "Character relationships"}}, "status": {"type": "string", "description": "Character's current status in the story", "required": true, "enum": ["Planned", "Introduced", "Active", "Inactive", "Completed", "Removed"]}, "notes": {"type": "array", "description": "Additional character notes", "required": false}}, "additionalProperties": true}