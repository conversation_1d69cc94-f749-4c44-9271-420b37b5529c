{"name": "add_inventory", "description": "A collection of items or equipment belonging to a character, entity, or location.", "properties": {"name": {"type": "string", "description": "[Entity]_inventory.", "required": true}, "owner": {"type": "string", "description": "The owner of this inventory.", "required": true, "relationship": {"edgeType": "owned_by", "description": "The entity that owns this inventory."}}, "items": {"type": "array", "description": "List of items in the inventory.", "required": true}}, "additionalProperties": true}