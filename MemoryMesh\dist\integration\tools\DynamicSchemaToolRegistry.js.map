{"version": 3, "file": "DynamicSchemaToolRegistry.js", "sourceRoot": "", "sources": ["../../../src/integration/tools/DynamicSchemaToolRegistry.ts"], "names": [], "mappings": "AAAA,yCAAyC;AAEzC,OAAO,EAAC,QAAQ,IAAI,EAAE,EAAC,MAAM,IAAI,CAAC;AAClC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EACH,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EACrB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAC,MAAM,EAAC,MAAM,kBAAkB,CAAC;AACxC,OAAO,EAAC,kBAAkB,EAAE,eAAe,EAAC,MAAM,kBAAkB,CAAC;AAcrE;;GAEG;AACH,MAAM,yBAAyB;IACnB,OAAO,CAA6B;IACpC,UAAU,CAAoB;IAC9B,MAAM,CAAC,QAAQ,CAA4B;IAEnD;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,CAAC;YACtC,yBAAyB,CAAC,QAAQ,GAAG,IAAI,yBAAyB,EAAE,CAAC;QACzE,CAAC;QACD,OAAO,yBAAyB,CAAC,QAAQ,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACnB,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;YAC7C,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAElD,uBAAuB;YACvB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;oBAChC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;oBACvD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;oBACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;YAED,iCAAiC;YACjC,KAAK,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;gBACxD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACpE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,CAAC;QACrH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,MAAqB;QAC1E,MAAM,KAAK,GAAW,EAAE,CAAC;QACzB,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC,WAAW;QACX,KAAK,CAAC,IAAI,CAAC,UAA6B,CAAC,CAAC;QAE1C,cAAc;QACd,MAAM,YAAY,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,YAA+B,CAAC,CAAC;QAE5C,cAAc;QACd,MAAM,YAAY,GAAS;YACvB,IAAI,EAAE,UAAU,UAAU,EAAE;YAC5B,WAAW,EAAE;;cAEX,UAAU;;;;kBAIN;YACN,WAAW,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,CAAC,UAAU,UAAU,EAAE,CAAC,EAAE;wBACtB,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,yBAAyB,UAAU,EAAE;wBAClD,UAAU,EAAE;4BACR,IAAI,EAAE;gCACF,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,mBAAmB,UAAU,YAAY;6BACzD;yBACJ;wBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;qBACrB;iBACJ;gBACD,QAAQ,EAAE,CAAC,UAAU,UAAU,EAAE,CAAC;aACrC;SACJ,CAAC;QAEF,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACvB,QAAgB,EAChB,IAAyB,EACzB,qBAAyC;QAEzC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,6BAA6B,QAAQ,EAAE;gBAC9C,WAAW,EAAE,CAAC,iEAAiE,CAAC;aACnF,CAAC,CAAC;QACP,CAAC;QAED,MAAM,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAEnD,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,qBAAqB,UAAU,EAAE;gBACxC,OAAO,EAAE,EAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAC;gBAC5D,WAAW,EAAE,CAAC,2BAA2B,CAAC;aAC7C,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;YAErC,QAAQ,SAAS,EAAE,CAAC;gBAChB,KAAK,KAAK,CAAC,CAAC,CAAC;oBACT,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;oBAClC,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;oBAE7E,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACjC,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC7D,CAAC;oBAED,MAAM,EAAC,KAAK,EAAE,KAAK,EAAC,GAAG,MAAM,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;oBAE5E,MAAM,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;oBAC/C,IAAI,CAAC;wBACD,MAAM,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACnB,MAAM,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBAChD,CAAC;wBACD,MAAM,qBAAqB,CAAC,MAAM,EAAE,CAAC;wBAErC,OAAO,kBAAkB,CAAC;4BACtB,IAAI,EAAE,EAAC,KAAK,EAAE,KAAK,EAAC;4BACpB,WAAW,EAAE,WAAW,UAAU,KAAK,QAAQ,CAAC,IAAI,EAAE;yBACzD,CAAC,CAAC;oBACP,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACb,MAAM,qBAAqB,CAAC,QAAQ,EAAE,CAAC;wBACvC,MAAM,KAAK,CAAC;oBAChB,CAAC;gBACL,CAAC;gBAED,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACZ,OAAO,kBAAkB,CACrB,IAAI,CAAC,UAAU,UAAU,EAAE,CAAC,EAC5B,MAAM,EACN,UAAU,EACV,qBAAqB,CACxB,CAAC;gBACN,CAAC;gBAED,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACZ,MAAM,EAAC,IAAI,EAAC,GAAG,IAAI,CAAC,UAAU,UAAU,EAAE,CAAC,CAAC;oBAC5C,IAAI,CAAC,IAAI,EAAE,CAAC;wBACR,OAAO,eAAe,CAAC;4BACnB,SAAS,EAAE,QAAQ;4BACnB,KAAK,EAAE,gCAAgC,UAAU,EAAE;4BACnD,WAAW,EAAE,CAAC,8BAA8B,CAAC;yBAChD,CAAC,CAAC;oBACP,CAAC;oBACD,OAAO,kBAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,qBAAqB,CAAC,CAAC;gBACvE,CAAC;gBAED;oBACI,OAAO,eAAe,CAAC;wBACnB,SAAS,EAAE,QAAQ;wBACnB,KAAK,EAAE,sBAAsB,SAAS,EAAE;wBACxC,WAAW,EAAE,CAAC,kCAAkC,CAAC;qBACpD,CAAC,CAAC;YACX,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gBACxE,OAAO,EAAE,EAAC,IAAI,EAAC;gBACf,WAAW,EAAE;oBACT,uCAAuC;oBACvC,6CAA6C;iBAChD;gBACD,aAAa,EAAE;oBACX,4BAA4B;oBAC5B,yCAAyC;iBAC5C;aACJ,CAAC,CAAC;QACP,CAAC;IACL,CAAC;CACJ;AAED,uCAAuC;AACvC,MAAM,CAAC,MAAM,kBAAkB,GAAG,yBAAyB,CAAC,WAAW,EAAE,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB;IACxC,MAAM,kBAAkB,CAAC,UAAU,EAAE,CAAC;IACtC,OAAO,kBAAkB,CAAC;AAC9B,CAAC"}