{"name": "add_artifact", "description": "Add a new artifact or unique item to the knowledge graph", "properties": {"name": {"type": "string", "description": "The artifact's name", "required": true}, "description": {"type": "string", "description": "A detailed description of the artifact", "required": true}, "type": {"type": "string", "description": "The artifact's type", "required": true}, "rarity": {"type": "string", "description": "The rarity of the artifact", "required": true}, "effects": {"type": "array", "description": "The artifact's effects or abilities", "required": true}, "origin": {"type": "string", "description": "The artifact's origin or history", "required": false}, "value": {"type": "string", "description": "The monetary or intrinsic value of the artifact", "required": false}, "relatedCharacters": {"type": "array", "description": "Characters associated with the artifact", "required": false, "relationship": {"edgeType": "owned_by", "description": "Artifact owners"}}, "relatedQuests": {"type": "array", "description": "Quests involving the artifact", "required": false, "relationship": {"edgeType": "associated_with", "description": "Artifact-related quests"}}, "relatedLocations": {"type": "array", "description": "Locations associated with the artifact", "required": false, "relationship": {"edgeType": "found_at", "description": "Artifact locations"}}}, "additionalProperties": true}