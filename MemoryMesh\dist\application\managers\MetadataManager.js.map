{"version": 3, "file": "MetadataManager.js", "sourceRoot": "", "sources": ["../../../src/application/managers/MetadataManager.ts"], "names": [], "mappings": "AAAA,uDAAuD;AAGvD,OAAO,EAAC,QAAQ,EAAC,MAAM,0BAA0B,CAAC;AAClD,OAAO,EAAC,cAAc,EAAC,MAAM,gBAAgB,CAAC;AAG9C;;;GAGG;AACH,MAAM,OAAO,eAAgB,SAAQ,QAAQ;IACzC;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAA4B;QAC1C,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAC,QAAQ,EAAC,CAAC,CAAC;YAE3C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAqB,EAAE,CAAC;YAErC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC1B,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE7D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjC,IAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACxB,CAAC;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/C,CAAC,IAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CACpC,CAAC;gBAEF,IAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;gBACpC,OAAO,CAAC,IAAI,CAAC;oBACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,aAAa,EAAE,WAAW;iBAC7B,CAAC,CAAC;YACP,CAAC;YAED,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAC,OAAO,EAAC,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,SAA6B;QAC9C,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAC,SAAS,EAAC,CAAC,CAAC;YAE/C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAC/B,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEjE,IAAI,IAAI,EAAE,CAAC;oBACP,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACrC,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CACjC,CAAC;oBACF,YAAY,IAAI,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAChE,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAC,YAAY,EAAC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAExD,OAAO,IAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;CACJ"}