{"version": 3, "file": "SchemaBuilder.js", "sourceRoot": "", "sources": ["../../../src/core/schema/SchemaBuilder.ts"], "names": [], "mappings": "AAAA,qCAAqC;AAoDrC;;GAEG;AACH,MAAM,OAAO,aAAa;IACd,MAAM,CAAwB;IAC9B,aAAa,CAAkC;IAC/C,cAAc,CAAiB;IAEvC;;OAEG;IACH,YAAY,IAAY,EAAE,WAAmB;QACzC,IAAI,CAAC,MAAM,GAAG;YACV,IAAI;YACJ,WAAW;YACX,WAAW,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE;wBACxB,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE,EAAE;wBACd,QAAQ,EAAE,EAAE;wBACZ,oBAAoB,EAAE;4BAClB,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,2BAA2B;yBAC3C;qBACJ;iBACJ;gBACD,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aACvC;SACJ,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG;YAClB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,aAAa,EAAE,EAAE;SACpB,CAAC;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CACb,IAAY,EACZ,WAAmB,EACnB,WAAoB,KAAK,EACzB,aAA8B,IAAI;QAElC,MAAM,QAAQ,GAAyB;YACnC,IAAI,EAAE,QAAQ;YACd,WAAW;SACd,CAAC;QAEF,IAAI,UAAU,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC;QAC/B,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;YAE3E,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,gBAAgB,CACZ,IAAY,EACZ,WAAmB,EACnB,WAAoB,KAAK,EACzB,aAA8B,IAAI;QAElC,MAAM,QAAQ,GAAyB;YACnC,IAAI,EAAE,OAAO;YACb,WAAW;YACX,KAAK,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,WAAW,IAAI,QAAQ;gBACpC,GAAG,CAAC,UAAU,IAAI,EAAC,IAAI,EAAE,UAAU,EAAC,CAAC;aACxC;SACJ,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;YAE3E,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CACX,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,WAA0B,IAAI;QAE9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE;YACjC,QAAQ;YACR,GAAG,CAAC,QAAQ,IAAI,EAAC,QAAQ,EAAC,CAAC;YAC3B,WAAW;SACd,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,OAAgB;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,oBAAoB,GAAG;oBAClE,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2BAA2B;iBAC3C,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAChF,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,gBAA6B,IAAI,GAAG,EAAE;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAChE,MAAM,mBAAmB,GAAG,IAAI,aAAa,CACzC,UAAU,EACV,sBAAsB,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,yBAAyB,CACnF,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;QAE7G,uCAAuC;QACvC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,EAAE;YAC7D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC7B,mBAAmB,CAAC,gBAAgB,CAChC,QAAQ,EACR,SAAS,CAAC,WAAW,EACrB,KAAK,EACL,SAAS,CAAC,KAAK,EAAE,IAAI,CACxB,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACJ,mBAAmB,CAAC,iBAAiB,CACjC,QAAQ,EACR,SAAS,CAAC,WAAW,EACrB,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAC;gBACN,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,mBAAmB,CAAC,eAAe,CAC/B,QAAQ,EACR,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,WAAW,IAAI,uBAAuB,EAC7C,MAAM,CAAC,QAAQ,IAAI,IAAI,CAC1B,CAAC;YACN,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,mBAAmB,CAAC,gBAAgB,CAChC,UAAU,EACV,gEAAgE,CACnE,CAAC;QAEF,OAAO,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK;QACD,OAAO;YACH,GAAG,IAAI,CAAC,MAAsB;YAC9B,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;YACrD,cAAc,EAAE,IAAI,CAAC,cAAc;SACtC,CAAC;IACN,CAAC;CACJ"}