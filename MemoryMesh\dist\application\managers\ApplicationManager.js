// src/application/managers/ApplicationManager.ts
import { GraphManager, SearchManager, TransactionManager } from "../index.js";
import { JsonLineStorage } from '../../infrastructure/index.js';
/**
 * Main facade that coordinates between specialized managers
 */
export class ApplicationManager {
    graphManager;
    searchManager;
    transactionManager;
    constructor(storage = new JsonLineStorage()) {
        this.graphManager = new GraphManager(storage);
        this.searchManager = new SearchManager(storage);
        this.transactionManager = new TransactionManager(storage);
    }
    // Graph operations delegated to GraphManager
    async addNodes(nodes) {
        return this.graphManager.addNodes(nodes);
    }
    async updateNodes(nodes) {
        return this.graphManager.updateNodes(nodes);
    }
    async deleteNodes(nodeNames) {
        return this.graphManager.deleteNodes(nodeNames);
    }
    async addEdges(edges) {
        return this.graphManager.addEdges(edges);
    }
    async updateEdges(edges) {
        return this.graphManager.updateEdges(edges);
    }
    async deleteEdges(edges) {
        return this.graphManager.deleteEdges(edges);
    }
    async getEdges(filter) {
        return this.graphManager.getEdges(filter);
    }
    async addMetadata(metadata) {
        return this.graphManager.addMetadata(metadata);
    }
    async deleteMetadata(deletions) {
        return this.graphManager.deleteMetadata(deletions);
    }
    // Search operations delegated to SearchManager
    async readGraph() {
        return this.searchManager.readGraph();
    }
    async searchNodes(query) {
        return this.searchManager.searchNodes(query);
    }
    async openNodes(names) {
        return this.searchManager.openNodes(names);
    }
    // Transaction operations delegated to TransactionManager
    async beginTransaction() {
        return this.transactionManager.beginTransaction();
    }
    async commit() {
        return this.transactionManager.commit();
    }
    async rollback() {
        return this.transactionManager.rollback();
    }
    async withTransaction(operation) {
        return this.transactionManager.withTransaction(operation);
    }
    async addRollbackAction(action, description) {
        return this.transactionManager.addRollbackAction(action, description);
    }
    isInTransaction() {
        return this.transactionManager.isInTransaction();
    }
    getCurrentGraph() {
        return this.transactionManager.getCurrentGraph();
    }
}
//# sourceMappingURL=ApplicationManager.js.map