{"name": "add_project", "description": "Add a new project, task, or initiative to the knowledge graph. Suitable for work projects, research, campaigns, or any goal-oriented activity.", "properties": {"name": {"type": "string", "description": "Project name or title", "required": true}, "description": {"type": "string", "description": "Detailed description of the project", "required": true}, "status": {"type": "string", "description": "Current status of the project", "required": true, "enum": ["Planning", "Active", "On Hold", "Completed", "Cancelled", "Delayed"]}, "priority": {"type": "string", "description": "Priority level of the project", "required": true, "enum": ["Critical", "High", "Medium", "Low"]}, "projectManager": {"type": "string", "description": "Project manager or lead", "required": false, "relationship": {"edgeType": "managed_by", "description": "Project management relationship"}}, "team": {"type": "array", "description": "Team members assigned to the project", "required": false, "relationship": {"edgeType": "assigned_to", "description": "Team assignment"}}, "startDate": {"type": "string", "description": "Project start date", "required": false}, "endDate": {"type": "string", "description": "Project end date or deadline", "required": false}, "budget": {"type": "string", "description": "Project budget or cost", "required": false}, "objectives": {"type": "array", "description": "Project objectives or goals", "required": false}, "deliverables": {"type": "array", "description": "Expected deliverables or outcomes", "required": false}, "milestones": {"type": "array", "description": "Key milestones or checkpoints", "required": false}, "risks": {"type": "array", "description": "Identified risks or challenges", "required": false}, "dependencies": {"type": "array", "description": "Dependencies on other projects or resources", "required": false, "relationship": {"edgeType": "depends_on", "description": "Project dependencies"}}, "stakeholders": {"type": "array", "description": "Key stakeholders or interested parties", "required": false, "relationship": {"edgeType": "stakeholder_of", "description": "Stakeholder relationship"}}, "category": {"type": "string", "description": "Project category or type", "required": false}, "progress": {"type": "string", "description": "Current progress percentage or status", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}}, "additionalProperties": true}