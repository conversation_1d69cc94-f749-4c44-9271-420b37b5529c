// src/tools/handlers/DynamicToolHandler.ts
import { BaseToolHandler } from './BaseToolHandler.js';
import { dynamicToolManager } from '../../index.js';
import { formatToolResponse, formatToolError } from '../../../shared/index.js';
export class DynamicToolHandler extends BaseToolHandler {
    async handleTool(name, args) {
        try {
            this.validateArguments(args);
            const toolResult = await dynamicToolManager.handleToolCall(name, args, this.knowledgeGraphManager);
            // 檢查是否已經是格式化的結果 (Cursor 相容性修復)
            if (toolResult?.isError !== undefined) {
                return toolResult;
            }
            return formatToolResponse({
                data: toolResult,
                actionTaken: `Executed dynamic tool: ${name}`
            });
        }
        catch (error) {
            return formatToolError({
                operation: name,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                context: { toolName: name, args },
                suggestions: [
                    "Examine the tool input parameters for correctness",
                    "Verify that the requested operation is supported"
                ],
                recoverySteps: [
                    "Adjust the input parameters based on the schema definition"
                ]
            });
        }
    }
}
//# sourceMappingURL=DynamicToolHandler.js.map