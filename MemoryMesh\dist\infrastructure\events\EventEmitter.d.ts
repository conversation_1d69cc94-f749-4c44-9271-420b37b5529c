import type { EventListener } from './EventTypes.ts';
/**
 * A simple event emitter implementation for managing event listeners and emitting events.
 */
export declare class EventEmitter {
    /**
     * Maps event names to their respective sets of listener functions.
     */
    private eventListeners;
    constructor();
    /**
     * Adds an event listener for the specified event.
     */
    on(eventName: string, listener: EventListener): () => void;
    /**
     * Removes an event listener for the specified event.
     */
    off(eventName: string, listener: EventListener): void;
    /**
     * Adds a one-time event listener that removes itself after being called.
     */
    once(eventName: string, listener: EventListener): () => void;
    /**
     * Emits an event with the specified data to all registered listeners.
     */
    emit(eventName: string, data?: any): boolean;
    /**
     * Removes all listeners for a specific event or all events.
     */
    removeAllListeners(eventName?: string): void;
    /**
     * Gets the number of listeners for a specific event.
     */
    listenerCount(eventName: string): number;
    /**
     * Gets all registered event names.
     */
    eventNames(): string[];
    /**
     * Gets all listeners for a specific event.
     */
    getListeners(eventName: string): EventListener[];
}
