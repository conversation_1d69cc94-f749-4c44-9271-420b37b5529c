{"name": "add_event", "description": "Add a new event, meeting, activity, or occurrence to the knowledge graph. Suitable for meetings, conferences, incidents, or any time-based activity.", "properties": {"name": {"type": "string", "description": "Event name or title", "required": true}, "description": {"type": "string", "description": "Detailed description of the event", "required": true}, "type": {"type": "string", "description": "Type of event", "required": true, "enum": ["Meeting", "Conference", "Workshop", "Training", "Presentation", "Social", "Incident", "Milestone", "Deadline", "Other"]}, "status": {"type": "string", "description": "Current status of the event", "required": true, "enum": ["Planned", "Confirmed", "In Progress", "Completed", "Cancelled", "Postponed"]}, "startDateTime": {"type": "string", "description": "Event start date and time", "required": false}, "endDateTime": {"type": "string", "description": "Event end date and time", "required": false}, "location": {"type": "string", "description": "Event location or venue", "required": false, "relationship": {"edgeType": "takes_place_at", "description": "Event location"}}, "organizer": {"type": "string", "description": "Event organizer or host", "required": false, "relationship": {"edgeType": "organized_by", "description": "Event organizer"}}, "attendees": {"type": "array", "description": "List of attendees or participants", "required": false, "relationship": {"edgeType": "attended_by", "description": "Event attendance"}}, "agenda": {"type": "array", "description": "Event agenda or schedule", "required": false}, "objectives": {"type": "array", "description": "Event objectives or goals", "required": false}, "outcomes": {"type": "array", "description": "Event outcomes or results", "required": false}, "resources": {"type": "array", "description": "Resources needed or used for the event", "required": false, "relationship": {"edgeType": "requires", "description": "Resource requirements"}}, "relatedProjects": {"type": "array", "description": "Related projects or initiatives", "required": false, "relationship": {"edgeType": "related_to", "description": "Project relationship"}}, "priority": {"type": "string", "description": "Event priority level", "required": false, "enum": ["Critical", "High", "Medium", "Low"]}, "cost": {"type": "string", "description": "Event cost or budget", "required": false}, "notes": {"type": "array", "description": "Additional notes or comments", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}}, "additionalProperties": true}