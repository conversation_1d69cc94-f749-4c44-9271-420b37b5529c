{"version": 3, "file": "EdgeManager.js", "sourceRoot": "", "sources": ["../../../src/application/managers/EdgeManager.ts"], "names": [], "mappings": "AAAA,0CAA0C;AAE1C,OAAO,EAAC,YAAY,EAAC,MAAM,kDAAkD,CAAC;AAC9E,OAAO,EAAC,cAAc,EAAE,eAAe,EAAC,MAAM,gBAAgB,CAAC;AAI/D;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,YAAY;IACzC;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,KAAa;QACxB,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;YAErC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAE7C,mEAAmE;YACnE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACjC,cAAc,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACnD,yBAAyB;gBACzB,OAAO,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,OAAO,EAAE,CAAC;YACd,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC1B,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpD,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC;YAED,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAC,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;YAC9C,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAmB;QACjC,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;YAExC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,YAAY,GAAW,EAAE,CAAC;YAEhC,KAAK,MAAM,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAC/C,QAAQ,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI;oBACjC,QAAQ,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE;oBAC7B,QAAQ,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,CAC5C,CAAC;gBAEF,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,mBAAmB,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACvG,CAAC;gBAED,iEAAiE;gBACjE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;oBACrB,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;gBACjE,CAAC;gBACD,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;oBACnB,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC/D,CAAC;gBAED,MAAM,WAAW,GAAS;oBACtB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI;oBACvD,EAAE,EAAE,UAAU,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ;oBACnE,MAAM,EAAE,UAAU,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM;iBACpG,CAAC;gBAEF,gDAAgD;gBAChD,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACnC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACvD,CAAC;gBAED,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;gBACrC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,CAAC;YAED,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAC,KAAK,EAAE,YAAY,EAAC,CAAC,CAAC;YACrD,OAAO,YAAY,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAa;QAC3B,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;YAExC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;YAE5C,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CACxC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACf,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;gBAC3B,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;gBACvB,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CACtC,CACJ,CAAC;YAEF,MAAM,YAAY,GAAG,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;YAE3D,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAC,YAAY,EAAC,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAmB;QAC9B,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAE7C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC,KAAK,CAAC;YACvB,CAAC;YAED,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBAC7B,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;oBAAE,OAAO,KAAK,CAAC;gBAC3D,IAAI,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE;oBAAE,OAAO,KAAK,CAAC;gBACrD,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;CACJ"}