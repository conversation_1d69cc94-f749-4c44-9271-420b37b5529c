{"name": "add_transportation", "description": "Represents a transportation owned or used by a character or entity.", "properties": {"name": {"type": "string", "description": "The name of the vehicle.", "required": true}, "description": {"type": "string", "description": "A brief description of the vehicle.", "required": true}, "owner": {"type": "string", "description": "The entity or character that owns this vehicle.", "required": true, "relationship": {"edgeType": "owned_by", "description": "The relationship between the vehicle and its owner."}}, "type": {"type": "string", "description": "The type or class of the vehicle (e.g., car, spaceship, boat, horse).", "required": true}}, "additionalProperties": true}