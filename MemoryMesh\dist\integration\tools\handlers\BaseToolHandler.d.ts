import type { ApplicationManager } from '../../../application/index.js';
export declare abstract class BaseToolHandler {
    protected knowledgeGraphManager: ApplicationManager;
    constructor(knowledgeGraphManager: ApplicationManager);
    abstract handleTool(name: string, args: Record<string, any>): Promise<any>;
    protected validateArguments(args: Record<string, any>): void;
    protected handleError(name: string, error: unknown): any;
}
