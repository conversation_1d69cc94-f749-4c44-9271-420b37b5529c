// src/application/managers/GraphManager.ts
import { BaseManager, GraphOperations } from '../index.js';
/**
 * Handles graph-specific operations (nodes, edges, metadata)
 */
export class GraphManager extends BaseManager {
    graphOperations;
    constructor(storage) {
        super(storage);
        const { node<PERSON><PERSON><PERSON>, edgeManager, metadataManager } = this.createManagers();
        this.graphOperations = new GraphOperations(nodeManager, edgeManager, metadataManager);
    }
    // Node operations
    async addNodes(nodes) {
        return this.graphOperations.addNodes(nodes);
    }
    async updateNodes(nodes) {
        return this.graphOperations.updateNodes(nodes);
    }
    async deleteNodes(nodeNames) {
        return this.graphOperations.deleteNodes(nodeNames);
    }
    // Edge operations
    async addEdges(edges) {
        return this.graphOperations.addEdges(edges);
    }
    async updateEdges(edges) {
        return this.graphOperations.updateEdges(edges);
    }
    async deleteEdges(edges) {
        return this.graphOperations.deleteEdges(edges);
    }
    async getEdges(filter) {
        return this.graphOperations.getEdges(filter);
    }
    // Metadata operations
    async addMetadata(metadata) {
        return this.graphOperations.addMetadata(metadata);
    }
    async deleteMetadata(deletions) {
        return this.graphOperations.deleteMetadata(deletions);
    }
}
//# sourceMappingURL=GraphManager.js.map