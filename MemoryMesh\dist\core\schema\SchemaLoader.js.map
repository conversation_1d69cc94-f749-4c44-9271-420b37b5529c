{"version": 3, "file": "SchemaLoader.js", "sourceRoot": "", "sources": ["../../../src/core/schema/SchemaLoader.ts"], "names": [], "mappings": "AAAA,oCAAoC;AAEpC,OAAO,EAAC,QAAQ,IAAI,EAAE,EAAC,MAAM,IAAI,CAAC;AAClC,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAC,MAAM,EAAC,MAAM,kBAAkB,CAAC;AAqBxC;;GAEG;AACH,MAAM,OAAO,YAAY;IACrB;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,UAAkB;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,UAAU,cAAc,CAAC,CAAC;QAEvE,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAc,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,yBAAyB,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,yBAAyB,UAAU,EAAE,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,MAAiB;QAC3C,MAAM,OAAO,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAEnE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,EAAE;YACjE,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC9B,OAAO,CAAC,gBAAgB,CACpB,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,IAAI,CAClB,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,iBAAiB,CACrB,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,IAAI,CAClB,CAAC;YACN,CAAC;YAED,8BAA8B;YAC9B,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC1B,OAAO,CAAC,eAAe,CACnB,QAAQ,EACR,UAAU,CAAC,YAAY,CAAC,QAAQ,EAChC,UAAU,CAAC,YAAY,CAAC,WAAW,EACnC,UAAU,CAAC,YAAY,CAAC,QAAQ,CACnC,CAAC;YACN,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc;QACvB,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;QAE7C,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;YAExE,MAAM,OAAO,GAAkC,EAAE,CAAC;YAClD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBACvD,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,OAAO,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,cAAc,CAAC,MAAiB;QAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,EAAE;YACjE,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,YAAY,QAAQ,iCAAiC,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,UAAU,CAAC,YAAY,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC/D,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,qBAAqB,CAAC,CAAC;YAC5E,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ"}