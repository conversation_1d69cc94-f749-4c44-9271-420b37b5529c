import type { Node, Edge, Graph, SchemaConfig } from '../index.js';
import type { ApplicationManager } from '../../application/index.js';
interface NodeData {
    name: string;
    [key: string]: any;
}
export interface ProcessedNodeResult {
    nodes: Node[];
    edges: Edge[];
}
export interface SchemaUpdateResult {
    metadata: string[];
    edgeChanges: {
        remove: Edge[];
        add: Edge[];
    };
}
/**
 * Creates a node based on schema definition and input data.
 */
export declare function createSchemaNode(data: NodeData, schema: SchemaConfig, nodeType: string): Promise<ProcessedNodeResult>;
export declare function updateSchemaNode(updates: NodeData, currentNode: Node, schema: SchemaConfig, currentGraph: Graph): Promise<SchemaUpdateResult>;
/**
 * Handles the complete update process for a schema-based entity.
 */
export declare function handleSchemaUpdate(updates: NodeData, schema: SchemaConfig, nodeType: string, applicationManager: ApplicationManager): Promise<any>;
export declare function handleSchemaDelete(nodeName: string, nodeType: string, applicationManager: ApplicationManager): Promise<any>;
export {};
