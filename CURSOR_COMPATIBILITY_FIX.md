# MemoryMesh Cursor 相容性修復指南

## 問題描述

MemoryMesh MCP 工具在 Cursor 中調用時會出現「no result from tool」錯誤，雖然後端 MCP Logs 顯示工具成功執行，但 AI 對話介面無法正確顯示回傳結果。

### 問題現象
- MCP Logs 顯示：`Successfully called tool 'xxx'`
- 視覺化介面能看到資料正確寫入
- Cursor AI 對話框顯示：`Error: no result from tool`
- 其他 MCP 工具正常運作

### 根本原因
MemoryMesh 使用的回傳格式與 Cursor 預期的標準 MCP 格式不相容：

**MemoryMesh 原始格式**：
```typescript
{
  toolResult: {
    isError: false,
    content: [{type: "text", text: "訊息"}],
    data: {...},
    timestamp: "...",
    suggestions: [...]
  }
}
```

**Cursor 預期格式**：
```typescript
{
  content: [
    {type: "text", text: "訊息"}
  ],
  isError: false
}
```

## 解決方案

### 修改檔案清單

需要修改以下檔案以實現格式相容性：

1. `src/shared/utils/responseFormatter.ts` - 核心格式化函數
2. `src/index.ts` - 主要入口點
3. `src/integration/tools/callToolHandler.ts` - 工具調用處理器
4. `src/integration/tools/handlers/BaseToolHandler.ts` - 基礎處理器
5. `src/integration/tools/handlers/DynamicToolHandler.ts` - 動態工具處理器
6. `src/integration/tools/handlers/SearchToolHandler.ts` - 搜尋工具處理器
7. `src/integration/tools/handlers/MetadataToolHandler.ts` - 元資料工具處理器
8. `src/integration/tools/handlers/GraphToolHandler.ts` - 圖形工具處理器

### 詳細修改步驟

#### 1. 修改 `src/shared/utils/responseFormatter.ts`

```typescript
// 修改 formatToolResponse 函數
export function formatToolResponse<T = any>({
                                                data,
                                                message,
                                                actionTaken,
                                                suggestions = []
                                            }: ToolResponseOptions<T>): any {
    // Create content array for display
    const content = [];
    
    if (message) {
        content.push({type: "text", text: message});
    }
    
    if (actionTaken) {
        content.push({type: "text", text: `Action taken: ${actionTaken}`});
    }
    
    if (data !== undefined) {
        content.push({type: "text", text: `Data: ${JSON.stringify(data, null, 2)}`});
    }
    
    if (suggestions.length > 0) {
        content.push({type: "text", text: `Suggestions: ${suggestions.join(', ')}`});
    }
    
    // Return simplified format compatible with Cursor
    return {
        content: content.length > 0 ? content : [{type: "text", text: "Operation completed successfully"}],
        isError: false
    };
}

// 修改 formatToolError 函數
export function formatToolError({
                                    operation,
                                    error,
                                    context,
                                    suggestions = [],
                                    recoverySteps = []
                                }: ToolErrorOptions): any {
    const content = [
        {type: "text", text: `Error during ${operation}: ${error}`}
    ];
    
    if (context) {
        content.push({type: "text", text: `Context: ${JSON.stringify(context, null, 2)}`});
    }
    
    if (suggestions.length > 0) {
        content.push({type: "text", text: `Suggestions: ${suggestions.join(', ')}`});
    }
    
    if (recoverySteps.length > 0) {
        content.push({type: "text", text: `Recovery steps: ${recoverySteps.join(', ')}`});
    }
    
    // Return simplified format compatible with Cursor
    return {
        content,
        isError: true
    };
}
```

#### 2. 修改 `src/index.ts`

```typescript
// 在 CallToolRequestSchema 處理器中
server.setRequestHandler(CallToolRequestSchema, async (request) => {
    try {
        // ... 其他代碼 ...
        
        const result = await handleCallToolRequest(toolRequest, knowledgeGraphManager);

        // 直接回傳結果，不包裝在 toolResult 中
        return result;
    } catch (error) {
        // ... 錯誤處理 ...
        const formattedError = formatToolError({...});
        return formattedError; // 直接回傳，不包裝
    }
});
```

#### 3. 修改所有工具處理器

將所有處理器的回傳類型從 `Promise<ToolResponse>` 改為 `Promise<any>`，並移除 `ToolResponse` 類型依賴。

### 自動化修復腳本

可以建立以下 PowerShell 腳本來自動應用修復：

```powershell
# apply-cursor-fix.ps1
Write-Host "正在應用 MemoryMesh Cursor 相容性修復..." -ForegroundColor Green

# 備份原始檔案
$backupDir = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force

$filesToBackup = @(
    "src/shared/utils/responseFormatter.ts",
    "src/index.ts",
    "src/integration/tools/callToolHandler.ts",
    "src/integration/tools/handlers/BaseToolHandler.ts",
    "src/integration/tools/handlers/DynamicToolHandler.ts",
    "src/integration/tools/handlers/SearchToolHandler.ts",
    "src/integration/tools/handlers/MetadataToolHandler.ts",
    "src/integration/tools/handlers/GraphToolHandler.ts"
)

foreach ($file in $filesToBackup) {
    if (Test-Path $file) {
        $backupPath = Join-Path $backupDir $file
        $backupParent = Split-Path $backupPath -Parent
        New-Item -ItemType Directory -Path $backupParent -Force
        Copy-Item $file $backupPath
        Write-Host "已備份: $file" -ForegroundColor Yellow
    }
}

Write-Host "備份完成，備份目錄: $backupDir" -ForegroundColor Green
Write-Host "請手動應用修改，然後執行: npm run build" -ForegroundColor Cyan
```

## 驗證修復

### 1. 重新建構專案
```bash
npm run build
```

### 2. 測試工具調用
```typescript
// 測試角色建立
mcp_memorymesh_add_player_character({
    "player_character": {
        "name": "測試角色",
        "age": "25",
        "gender": "男性",
        "occupation": "戰士",
        "status": "活躍"
    }
})

// 測試搜尋功能
mcp_memorymesh_search_nodes({"query": "測試"})
```

### 3. 預期結果
- 不再出現「no result from tool」錯誤
- 能在 Cursor AI 對話框中看到詳細的操作結果
- 包含動作描述和資料內容

## 覆蓋範圍

此修復方案覆蓋：

✅ **所有現有工具**：
- 核心工具（add_nodes, update_nodes, delete_nodes 等）
- 搜尋工具（search_nodes, read_graph, open_nodes）
- 元資料工具（add_metadata, delete_metadata）
- 所有動態生成的 schema 工具

✅ **未來新增的工具**：
- 任何新增的 `.schema.json` 檔案生成的工具
- 自動使用修復後的回傳格式

## 注意事項

1. **備份重要性**：修改前務必備份原始檔案
2. **重新建構**：修改後必須執行 `npm run build`
3. **重啟 Cursor**：建議重啟 Cursor Desktop 以確保變更生效
4. **版本相容性**：此修復適用於 MemoryMesh v0.2.8，其他版本可能需要調整

## 故障排除

### 問題：修改後仍然無法顯示結果
**解決方案**：
1. 確認所有檔案都已正確修改
2. 重新執行 `npm run build`
3. 完全重啟 Cursor Desktop
4. 檢查 MCP Logs 確認工具調用成功

### 問題：編譯錯誤
**解決方案**：
1. 檢查 TypeScript 語法錯誤
2. 確認所有 import 語句正確
3. 恢復備份檔案重新開始

### 問題：部分工具仍有問題
**解決方案**：
1. 檢查是否遺漏修改某個處理器
2. 確認 ToolHandlerFactory 路由正確
3. 查看具體錯誤訊息進行針對性修復

## 更新記錄

- **2025-06-15**：初始版本，修復 Cursor 相容性問題
- 適用版本：MemoryMesh v0.2.8
- 測試環境：Windows 10, Cursor, PowerShell

---

**重要提醒**：此修復方案是針對 MemoryMesh 與 Cursor 的相容性問題。如果 MemoryMesh 或 Cursor 有重大更新，可能需要重新評估和調整此修復方案。 