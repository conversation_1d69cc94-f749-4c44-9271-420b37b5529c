import type { IStorage } from './IStorage.js';
import type { Edge, Graph } from '../../core/index.js';
/**
 * Handles persistent storage of the knowledge graph using a JSON Lines file format.
 */
export declare class JsonLineStorage implements IStorage {
    private edgeIndex;
    private initialized;
    constructor();
    /**
     * Ensures the storage file and directory exist
     */
    private ensureStorageExists;
    /**
     * Loads the entire knowledge graph from storage and builds the edge indices.
     */
    loadGraph(): Promise<Graph>;
    /**
     * Saves the entire knowledge graph to storage.
     */
    saveGraph(graph: Graph): Promise<void>;
    /**
     * Loads specific edges by their IDs from storage.
     */
    loadEdgesByIds(edgeIds: string[]): Promise<Edge[]>;
    /**
     * Indexes a single edge by adding it to all relevant indices.
     */
    private indexEdge;
    /**
     * Generates a unique ID for an edge based on its properties.
     */
    private generateEdgeId;
    /**
     * Clears all edge indices.
     */
    private clearIndices;
}
