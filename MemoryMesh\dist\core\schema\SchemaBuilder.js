// src/schema/loader/schemaBuilder.ts
/**
 * Facilitates the construction and manipulation of schemas for nodes in the knowledge graph.
 */
export class SchemaBuilder {
    schema;
    relationships;
    metadataConfig;
    /**
     * Creates an instance of SchemaBuilder.
     */
    constructor(name, description) {
        this.schema = {
            name,
            description,
            inputSchema: {
                type: "object",
                properties: {
                    [name.replace('add_', '')]: {
                        type: "object",
                        properties: {},
                        required: [],
                        additionalProperties: {
                            type: "string",
                            description: "Any additional properties"
                        }
                    }
                },
                required: [name.replace('add_', '')]
            }
        };
        this.relationships = new Map();
        this.metadataConfig = {
            requiredFields: [],
            optionalFields: [],
            excludeFields: []
        };
    }
    /**
     * Adds a string property to the schema with an optional enum.
     */
    addStringProperty(name, description, required = false, enumValues = null) {
        const property = {
            type: "string",
            description
        };
        if (enumValues) {
            property.enum = enumValues;
        }
        const schemaName = this.schema.name.replace('add_', '');
        if (this.schema.inputSchema?.properties[schemaName]) {
            this.schema.inputSchema.properties[schemaName].properties[name] = property;
            if (required) {
                this.schema.inputSchema.properties[schemaName].required.push(name);
                this.metadataConfig.requiredFields.push(name);
            }
            else {
                this.metadataConfig.optionalFields.push(name);
            }
        }
        return this;
    }
    /**
     * Adds an array property to the schema with optional enum values for items.
     */
    addArrayProperty(name, description, required = false, enumValues = null) {
        const property = {
            type: "array",
            description,
            items: {
                type: "string",
                description: `Item in ${name} array`,
                ...(enumValues && { enum: enumValues })
            }
        };
        const schemaName = this.schema.name.replace('add_', '');
        if (this.schema.inputSchema?.properties[schemaName]) {
            this.schema.inputSchema.properties[schemaName].properties[name] = property;
            if (required) {
                this.schema.inputSchema.properties[schemaName].required.push(name);
                this.metadataConfig.requiredFields.push(name);
            }
            else {
                this.metadataConfig.optionalFields.push(name);
            }
        }
        return this;
    }
    /**
     * Adds a relationship definition to the schema.
     */
    addRelationship(propertyName, edgeType, description, nodeType = null) {
        this.relationships.set(propertyName, {
            edgeType,
            ...(nodeType && { nodeType }),
            description
        });
        this.metadataConfig.excludeFields.push(propertyName);
        return this.addArrayProperty(propertyName, description);
    }
    /**
     * Sets whether additional properties are allowed in the schema.
     */
    allowAdditionalProperties(allowed) {
        const schemaName = this.schema.name.replace('add_', '');
        if (this.schema.inputSchema?.properties[schemaName]) {
            if (allowed) {
                this.schema.inputSchema.properties[schemaName].additionalProperties = {
                    type: "string",
                    description: "Additional property value"
                };
            }
            else {
                this.schema.inputSchema.properties[schemaName].additionalProperties = false;
            }
        }
        return this;
    }
    /**
     * Creates an update schema based on the current schema.
     */
    createUpdateSchema(excludeFields = new Set()) {
        const schemaName = this.schema.name.replace('add_', 'update_');
        const updateSchemaBuilder = new SchemaBuilder(schemaName, `Update an existing ${schemaName.replace('update_', '')} in the knowledge graph`);
        const baseProperties = this.schema.inputSchema.properties[this.schema.name.replace('add_', '')].properties;
        // Copy properties except excluded ones
        Object.entries(baseProperties).forEach(([propName, propValue]) => {
            if (!excludeFields.has(propName)) {
                if (propValue.type === 'array') {
                    updateSchemaBuilder.addArrayProperty(propName, propValue.description, false, propValue.items?.enum);
                }
                else {
                    updateSchemaBuilder.addStringProperty(propName, propValue.description, false, propValue.enum);
                }
            }
        });
        // Copy relationships
        this.relationships.forEach((config, propName) => {
            if (!excludeFields.has(propName)) {
                updateSchemaBuilder.addRelationship(propName, config.edgeType, config.description || 'Relationship property', config.nodeType || null);
            }
        });
        // Add metadata array
        updateSchemaBuilder.addArrayProperty('metadata', 'An array of metadata contents to replace the existing metadata');
        return updateSchemaBuilder.build();
    }
    /**
     * Builds and returns the final schema object.
     */
    build() {
        return {
            ...this.schema,
            relationships: Object.fromEntries(this.relationships),
            metadataConfig: this.metadataConfig
        };
    }
}
//# sourceMappingURL=SchemaBuilder.js.map