import { IManager } from './interfaces/IManager.js';
import { INodeManager } from './interfaces/INodeManager.js';
import type { Node } from '../../core/index.js';
/**
 * Implements node-related operations for the knowledge graph.
 * Includes adding, updating, deleting, and retrieving nodes.
 */
export declare class NodeManager extends IManager implements INodeManager {
    /**
     * Adds new nodes to the knowledge graph.
     */
    addNodes(nodes: Node[]): Promise<Node[]>;
    /**
     * Updates existing nodes in the knowledge graph.
     */
    updateNodes(nodes: Partial<Node>[]): Promise<Node[]>;
    /**
     * Deletes nodes and their associated edges from the knowledge graph.
     */
    deleteNodes(nodeNames: string[]): Promise<void>;
    /**
     * Retrieves specific nodes from the knowledge graph by their names.
     */
    getNodes(nodeNames: string[]): Promise<Node[]>;
}
