import { IMetadataManager } from './interfaces/IMetadataManager.js';
import { IManager } from './interfaces/IManager.js';
import type { Metadata, MetadataAddition, MetadataResult, MetadataDeletion } from '../../core/index.js';
/**
 * Implements metadata-related operations for the knowledge graph.
 * Includes adding, deleting, and retrieving metadata associated with nodes.
 */
export declare class MetadataManager extends IManager implements IMetadataManager {
    /**
     * Adds metadata to existing nodes.
     */
    addMetadata(metadata: MetadataAddition[]): Promise<MetadataResult[]>;
    /**
     * Deletes metadata from nodes.
     */
    deleteMetadata(deletions: MetadataDeletion[]): Promise<void>;
    /**
     * Retrieves metadata for a specific node.
     */
    getMetadata(nodeName: string): Promise<Metadata>;
}
