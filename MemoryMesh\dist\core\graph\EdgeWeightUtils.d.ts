import type { Edge } from './Edge.js';
/**
 * Utility functions for working with edge weights in the knowledge graph
 */
export declare class EdgeWeightUtils {
    /**
     * Validates that a weight is within the valid range (0-1)
     */
    static validateWeight(weight: number): void;
    /**
     * Sets a default weight for an edge if none is provided
     */
    static ensureWeight(edge: Edge): Edge;
    /**
     * Updates the weight of an edge based on new evidence
     * Uses a simple averaging approach
     */
    static updateWeight(currentWeight: number, newEvidence: number): number;
    /**
     * Combines multiple edge weights (e.g., for parallel edges)
     * Uses the maximum weight by default
     */
    static combineWeights(weights: number[]): number;
}
