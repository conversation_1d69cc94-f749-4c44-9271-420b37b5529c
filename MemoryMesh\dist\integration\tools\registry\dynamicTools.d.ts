import { type IDynamicSchemaToolRegistry } from '../../tools/DynamicSchemaToolRegistry.js';
import type { Tool, ToolResponse } from '../../../shared/index.js';
import type { ApplicationManager } from '../../../application/index.js';
/**
 * Manages dynamically generated tools based on schemas
 */
export declare class DynamicToolManager {
    private static instance;
    private registry;
    private initialized;
    private constructor();
    /**
     * Gets the singleton instance of DynamicToolManager
     */
    static getInstance(): DynamicToolManager;
    /**
     * Initializes the dynamic tool registry
     */
    initialize(): Promise<void>;
    /**
     * Gets all dynamically generated tools
     */
    getTools(): Tool[];
    /**
     * Handles a call to a dynamic tool
     */
    handleToolCall(toolName: string, args: Record<string, any>, knowledgeGraphManager: ApplicationManager): Promise<any>;
    /**
     * Checks if a tool name corresponds to a dynamic tool
     */
    isDynamicTool(toolName: string): boolean;
}
/**
 * Singleton instance of the DynamicToolManager
 */
export declare const dynamicToolManager: DynamicToolManager;
/**
 * Re-export types that might be needed by consumers
 */
export type { Tool, ToolResponse, IDynamicSchemaToolRegistry };
