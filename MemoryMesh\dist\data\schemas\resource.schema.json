{"name": "add_resource", "description": "Add a new resource, asset, item, or material to the knowledge graph. Suitable for equipment, supplies, digital assets, or any valuable item.", "properties": {"name": {"type": "string", "description": "Resource name or identifier", "required": true}, "type": {"type": "string", "description": "Type or category of resource", "required": true, "enum": ["Equipment", "Software", "Document", "Material", "Tool", "Vehicle", "Digital Asset", "Consumable", "Infrastructure", "Other"]}, "description": {"type": "string", "description": "Detailed description of the resource", "required": true}, "status": {"type": "string", "description": "Current status of the resource", "required": true, "enum": ["Available", "In Use", "Maintenance", "Reserved", "Damaged", "Retired", "Lost"]}, "owner": {"type": "string", "description": "Current owner or responsible person", "required": false, "relationship": {"edgeType": "owned_by", "description": "Ownership relationship"}}, "location": {"type": "string", "description": "Current location of the resource", "required": false, "relationship": {"edgeType": "located_at", "description": "Resource location"}}, "value": {"type": "string", "description": "Monetary value or cost", "required": false}, "quantity": {"type": "string", "description": "Quantity or amount available", "required": false}, "specifications": {"type": "array", "description": "Technical specifications or features", "required": false}, "purchaseDate": {"type": "string", "description": "Date of purchase or acquisition", "required": false}, "warrantyExpiry": {"type": "string", "description": "Warranty expiration date", "required": false}, "maintenanceSchedule": {"type": "string", "description": "Maintenance schedule or frequency", "required": false}, "supplier": {"type": "string", "description": "Supplier or vendor", "required": false, "relationship": {"edgeType": "supplied_by", "description": "Supplier relationship"}}, "serialNumber": {"type": "string", "description": "Serial number or unique identifier", "required": false}, "tags": {"type": "array", "description": "Tags for categorization", "required": false}, "notes": {"type": "array", "description": "Additional notes or comments", "required": false}}, "additionalProperties": true}