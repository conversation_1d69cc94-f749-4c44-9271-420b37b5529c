import type { ToolResponse, ToolResponseOptions, ToolErrorOptions, PartialSuccessOptions } from '../index.js';
/**
 * Formats successful tool responses in a consistent, AI-friendly way.
 * Modified for Cursor compatibility - returns simplified format.
 */
export declare function formatToolResponse<T = any>({ data, message, actionTaken, suggestions }: ToolResponseOptions<T>): any;
/**
 * Formats error responses in a consistent, AI-friendly way.
 * Modified for Cursor compatibility - returns simplified format.
 */
export declare function formatToolError({ operation, error, context, suggestions, recoverySteps }: ToolErrorOptions): any;
/**
 * Creates an informative message for partial success scenarios.
 */
export declare function formatPartialSuccess<T>({ operation, attempted, succeeded, failed, details }: PartialSuccessOptions<T>): ToolResponse;
